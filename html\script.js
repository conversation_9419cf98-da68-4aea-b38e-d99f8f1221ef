// Global variables
let currentSection = null
let progress = 0
let isPlaying = false
let isMuted = false
let currentTrackIndex = 0
let youtubePlayer = null
let isPlaylistOpen = false
let currentTime = 0
let duration = 0

// Initialize page with config data
function initializePage() {
  if (!window.Config) {
    console.error('Config not loaded!')
    return
  }

  // Set footer text
  const footerText = document.getElementById('footerText')
  footerText.textContent = Config.general.copyright + ' | ' + Config.general.version

  // Set background video
  if (Config.backgroundVideo.enabled) {
    const videoElement = document.getElementById('backgroundVideo')
    const videoParams = new URLSearchParams({
      autoplay: Config.backgroundVideo.autoplay ? '1' : '0',
      mute: Config.backgroundVideo.muted ? '1' : '0',
      loop: Config.backgroundVideo.loop ? '1' : '0',
      playlist: Config.backgroundVideo.youtubeId,
      controls: '0',
      showinfo: '0',
      rel: '0',
      iv_load_policy: '3',
      modestbranding: '1'
    })

    videoElement.src = `https://www.youtube.com/embed/${Config.backgroundVideo.youtubeId}?${videoParams.toString()}`
  }

  // Initialize music player
  if (Config.musicPlayer.enabled && Config.musicPlayer.tracks.length > 0) {
    initializeMusicPlayer()
  }
}

// YouTube API Ready
function onYouTubeIframeAPIReady() {
  if (Config.musicPlayer.enabled && Config.musicPlayer.tracks.length > 0) {
    initializeYouTubePlayer()
  }
}

// Initialize YouTube player
function initializeYouTubePlayer() {
  youtubePlayer = new YT.Player('youtubePlayer', {
    height: '0',
    width: '0',
    videoId: Config.musicPlayer.tracks[currentTrackIndex].youtubeId,
    playerVars: {
      autoplay: 0,
      controls: 0,
      disablekb: 1,
      fs: 0,
      modestbranding: 1,
      rel: 0,
      showinfo: 0
    },
    events: {
      onReady: onPlayerReady,
      onStateChange: onPlayerStateChange
    }
  })
}

// Player ready event
function onPlayerReady(event) {
  initializeMusicPlayer()
  generatePlaylist()

  // Set volume
  youtubePlayer.setVolume(Config.musicPlayer.volume * 100)

  // Auto play if enabled
  if (Config.musicPlayer.autoplay) {
    setTimeout(() => {
      youtubePlayer.playVideo()
    }, 1000) // 1 saniye bekle sonra başlat
  }

  // Start time update interval
  setInterval(updateProgress, 1000)
}

// Player state change event
function onPlayerStateChange(event) {
  if (event.data === YT.PlayerState.PLAYING) {
    isPlaying = true
    updatePlayButton()

    // Update duration when video starts playing
    setTimeout(() => {
      const videoDuration = youtubePlayer.getDuration()
      if (videoDuration > 0) {
        const track = Config.musicPlayer.tracks[currentTrackIndex]
        track.duration = formatTime(videoDuration)
        document.getElementById('totalTime').textContent = track.duration

        // Update playlist if needed
        updatePlaylistSelection()
      }
    }, 1000)

  } else if (event.data === YT.PlayerState.PAUSED) {
    isPlaying = false
    updatePlayButton()
  } else if (event.data === YT.PlayerState.ENDED) {
    nextTrack()
  }
}

// Initialize music player UI
function initializeMusicPlayer() {
  if (!Config.musicPlayer.enabled || !Config.musicPlayer.tracks.length) {
    document.getElementById('musicPlayer').style.display = 'none'
    return
  }

  // Load track info for all tracks
  loadAllTracksInfo()
  loadTrackInfo()
}

// Load all tracks info from YouTube
async function loadAllTracksInfo() {
  for (let i = 0; i < Config.musicPlayer.tracks.length; i++) {
    const track = Config.musicPlayer.tracks[i]
    if (!track.name || !track.artist) {
      await fetchYouTubeInfo(track, i)
    }
  }
}

// Fetch YouTube video info
async function fetchYouTubeInfo(track, index) {
  try {
    // Get basic info from YouTube thumbnail API
    const thumbnailUrl = `https://img.youtube.com/vi/${track.youtubeId}/maxresdefault.jpg`
    track.thumbnail = thumbnailUrl

    // Try to get video info from YouTube oEmbed API
    const oEmbedUrl = `https://www.youtube.com/oembed?url=https://www.youtube.com/watch?v=${track.youtubeId}&format=json`

    try {
      const response = await fetch(oEmbedUrl)
      if (response.ok) {
        const data = await response.json()
        track.name = data.title || `Track ${index + 1}`
        track.artist = data.author_name || 'Unknown Artist'
      } else {
        throw new Error('oEmbed failed')
      }
    } catch (e) {
      // Fallback to default values
      track.name = `YouTube Track ${index + 1}`
      track.artist = 'YouTube'
    }

    // Set default duration (will be updated when video loads)
    track.duration = '0:00'

  } catch (error) {
    console.log('Error fetching YouTube info:', error)
    track.name = `Track ${index + 1}`
    track.artist = 'Unknown'
    track.duration = '0:00'
    track.thumbnail = `https://img.youtube.com/vi/${track.youtubeId}/maxresdefault.jpg`
  }
}

// Load track info
function loadTrackInfo() {
  const track = Config.musicPlayer.tracks[currentTrackIndex]
  document.getElementById('trackName').textContent = track.name || 'Loading...'
  document.getElementById('trackArtist').textContent = track.artist || 'Loading...'
  document.getElementById('totalTime').textContent = track.duration || '0:00'

  // Update thumbnail
  const thumbnail = document.getElementById('trackThumbnail')
  const defaultIcon = document.getElementById('defaultIcon')

  if (Config.musicPlayer.showThumbnails && track.thumbnail) {
    thumbnail.src = track.thumbnail
    thumbnail.style.display = 'block'
    defaultIcon.style.display = 'none'
  } else {
    thumbnail.style.display = 'none'
    defaultIcon.style.display = 'block'
  }
}

// Initialize loading progress
function initializeLoading() {
  const loadingSteps = Config.loadingSteps
  const loadingSpeed = Config.general.loadingSpeed

  const interval = setInterval(() => {
    progress += Math.random() * 2

    if (progress >= 100) {
      progress = 100
      clearInterval(interval)
    }

    // Update progress bar (eski loading bar)
    const progressFill = document.getElementById("progressFill")
    const progressText = document.getElementById("progressText")
    if (progressFill) progressFill.style.width = progress + "%"
    if (progressText) progressText.textContent = Math.round(progress) + "%"

    // Update loading circle
    updateLoadingCircle(progress)

    // Update loading percentage
    const loadingPercentage = document.getElementById("loadingPercentage")
    if (loadingPercentage) loadingPercentage.textContent = Math.round(progress) + "%"

    // Update loading step
    const stepIndex = Math.floor((progress / 100) * loadingSteps.length)
    const currentStep = loadingSteps[Math.min(stepIndex, loadingSteps.length - 1)]
    const loadingStep = document.getElementById("loadingStep")
    if (loadingStep) loadingStep.textContent = currentStep
  }, loadingSpeed)
}

// Update loading circle
function updateLoadingCircle(progressValue) {
  const circle = document.getElementById('loadingCircleProgress')
  if (circle) {
    const circumference = 2 * Math.PI * 45 // r=45
    const offset = circumference - (progressValue / 100) * circumference
    circle.style.strokeDashoffset = offset
  }
}

// Toggle section
function toggleSection(section) {
  const panel = document.getElementById("sidePanel")
  const chevron = document.getElementById(section + "-chevron")

  if (currentSection === section) {
    // Close current section
    closePanel()
    return
  }

  // Reset all chevrons
  document.querySelectorAll(".menu-chevron").forEach((ch) => {
    ch.classList.remove("rotated")
    ch.classList.remove("fa-chevron-left")
    ch.classList.add("fa-chevron-right")
  })

  // Update current chevron
  chevron.classList.add("rotated")
  chevron.classList.remove("fa-chevron-right")
  chevron.classList.add("fa-chevron-left")

  currentSection = section
  loadPanelContent(section)
  panel.classList.add("open")
}

// Close panel
function closePanel() {
  const panel = document.getElementById("sidePanel")
  panel.classList.remove("open")
  currentSection = null

  // Reset all chevrons
  document.querySelectorAll(".menu-chevron").forEach((chevron) => {
    chevron.classList.remove("rotated")
    chevron.classList.remove("fa-chevron-left")
    chevron.classList.add("fa-chevron-right")
  })
}

// Load panel content
function loadPanelContent(section) {
  const titleElement = document.getElementById("panelTitle")
  const contentElement = document.getElementById("panelContent")

  let title = ""
  let content = ""

  switch (section) {
    case "announcements":
      title = "Duyurular"
      content = generateAnnouncementsContent()
      break
    case "rules":
      title = "Sunucu Kuralları"
      content = generateRulesContent()
      break
    case "social":
      title = "Topluluk"
      content = generateSocialContent()
      break
  }

  titleElement.textContent = title
  contentElement.innerHTML = content
}

// Generate announcements content
function generateAnnouncementsContent() {
  return Config.announcements
    .map(
      (announcement) => `
        <div class="announcement-item">
            <div class="announcement-header">
                <i class="${announcement.icon} announcement-icon ${announcement.color}"></i>
                <div class="announcement-content">
                    <h4 class="announcement-title">${announcement.title}</h4>
                    <p class="announcement-text">${announcement.content}</p>
                    <p class="announcement-date">${announcement.date}</p>
                </div>
            </div>
        </div>
    `,
    )
    .join("")
}

// Generate rules content
function generateRulesContent() {
  return Config.serverRules
    .map(
      (rule, index) => `
        <div class="rule-item">
            <div class="rule-number">
                <span>${index + 1}</span>
            </div>
            <p class="rule-text">${rule}</p>
        </div>
    `,
    )
    .join("")
}

// Generate social content
function generateSocialContent() {
  return Config.socialLinks
    .map(
      (link) => `
        <a href="${link.url}" target="_blank" class="social-btn ${link.class}">
            <i class="${link.icon}"></i>
            <span>${link.name}</span>
        </a>
    `,
    )
    .join("")
}

// Update progress
function updateProgress() {
  if (!youtubePlayer || !youtubePlayer.getCurrentTime) return

  currentTime = youtubePlayer.getCurrentTime()
  duration = youtubePlayer.getDuration()

  if (duration > 0) {
    const progressPercent = (currentTime / duration) * 100
    document.getElementById('progressTrackFill').style.width = progressPercent + '%'
    document.getElementById('currentTime').textContent = formatTime(currentTime)
  }
}

// Format time
function formatTime(seconds) {
  const mins = Math.floor(seconds / 60)
  const secs = Math.floor(seconds % 60)
  return `${mins}:${secs.toString().padStart(2, '0')}`
}

// Update play button
function updatePlayButton() {
  const playIcon = document.getElementById("playIcon")
  if (isPlaying) {
    playIcon.classList.remove("fa-play")
    playIcon.classList.add("fa-pause")
  } else {
    playIcon.classList.remove("fa-pause")
    playIcon.classList.add("fa-play")
  }
}

// Music player functions
function togglePlay() {
  if (!youtubePlayer) return

  if (isPlaying) {
    youtubePlayer.pauseVideo()
  } else {
    youtubePlayer.playVideo()
  }
}

function toggleMute() {
  if (!youtubePlayer) return

  isMuted = !isMuted
  const muteIcon = document.getElementById("muteIcon")

  if (isMuted) {
    muteIcon.classList.remove("fa-volume-up")
    muteIcon.classList.add("fa-volume-mute")
    youtubePlayer.setVolume(0)
  } else {
    muteIcon.classList.remove("fa-volume-mute")
    muteIcon.classList.add("fa-volume-up")
    youtubePlayer.setVolume(Config.musicPlayer.volume * 100)
  }
}

function previousTrack() {
  if (!Config.musicPlayer.enabled || !Config.musicPlayer.tracks.length) return

  currentTrackIndex = currentTrackIndex > 0 ? currentTrackIndex - 1 : Config.musicPlayer.tracks.length - 1
  loadTrack()
}

function nextTrack() {
  if (!Config.musicPlayer.enabled || !Config.musicPlayer.tracks.length) return

  currentTrackIndex = currentTrackIndex < Config.musicPlayer.tracks.length - 1 ? currentTrackIndex + 1 : 0
  loadTrack()
}

function loadTrack() {
  if (!youtubePlayer) return

  const track = Config.musicPlayer.tracks[currentTrackIndex]
  youtubePlayer.loadVideoById(track.youtubeId)
  loadTrackInfo()
  updatePlaylistSelection()
}

function togglePlaylist() {
  isPlaylistOpen = !isPlaylistOpen
  const playlist = document.getElementById('playlist')

  if (isPlaylistOpen) {
    playlist.classList.add('open')
  } else {
    playlist.classList.remove('open')
  }
}

function generatePlaylist() {
  const playlistContent = document.getElementById('playlistContent')

  const playlistHTML = Config.musicPlayer.tracks.map((track, index) => `
    <div class="playlist-item ${index === currentTrackIndex ? 'active' : ''}" onclick="selectTrack(${index})">
      <div class="playlist-item-thumbnail">
        <img src="${track.thumbnail || `https://img.youtube.com/vi/${track.youtubeId}/maxresdefault.jpg`}" alt="${track.name || 'Track'}">
      </div>
      <div class="playlist-item-info">
        <div class="playlist-item-name">${track.name || 'Loading...'}</div>
        <div class="playlist-item-artist">${track.artist || 'Loading...'}</div>
      </div>
      <div class="playlist-item-duration">${track.duration || '0:00'}</div>
    </div>
  `).join('')

  playlistContent.innerHTML = playlistHTML
}

function selectTrack(index) {
  currentTrackIndex = index
  loadTrack()
  togglePlaylist()
}

function updatePlaylistSelection() {
  const items = document.querySelectorAll('.playlist-item')
  items.forEach((item, index) => {
    if (index === currentTrackIndex) {
      item.classList.add('active')
    } else {
      item.classList.remove('active')
    }
  })
}

// Progress bar click handler
function setupProgressBarClick() {
  const progressTrack = document.getElementById('progressTrack')
  if (progressTrack) {
    progressTrack.addEventListener('click', (e) => {
      if (!youtubePlayer || !duration) return

      const rect = progressTrack.getBoundingClientRect()
      const clickX = e.clientX - rect.left
      const percentage = clickX / rect.width
      const seekTime = duration * percentage

      youtubePlayer.seekTo(seekTime)
    })
  }
}

// Initialize when page loads
document.addEventListener("DOMContentLoaded", () => {
  // Wait for config to load
  if (window.Config) {
    initializePage()
    initializeLoading()
    setupProgressBarClick()
  } else {
    // Retry after a short delay if config not loaded
    setTimeout(() => {
      if (window.Config) {
        initializePage()
        initializeLoading()
        setupProgressBarClick()
      }
    }, 100)
  }
})

// Handle escape key to close panel
document.addEventListener("keydown", (event) => {
  if (event.key === "Escape" && currentSection && Config.general.enableEscapeKey) {
    closePanel()
  }
})

// FiveM NUI Message Handler
window.addEventListener('message', (event) => {
  const data = event.data

  switch(data.type) {
    case 'loadProgress':
      // Update progress from client.lua
      progress = data.progress

      // Update old progress bar (if exists)
      const progressFill = document.getElementById("progressFill")
      const progressText = document.getElementById("progressText")
      if (progressFill) progressFill.style.width = progress + "%"
      if (progressText) progressText.textContent = Math.round(progress) + "%"

      // Update loading circle
      updateLoadingCircle(progress)

      // Update loading percentage
      const loadingPercentage = document.getElementById("loadingPercentage")
      if (loadingPercentage) loadingPercentage.textContent = Math.round(progress) + "%"

      // Update loading step if provided
      if (data.step) {
        const loadingStep = document.getElementById("loadingStep")
        if (loadingStep) loadingStep.textContent = data.step
      }
      break

    case 'loadingComplete':
      // Loading completed, prepare for fade out
      setTimeout(() => {
        document.body.style.opacity = '0'
        document.body.style.transition = 'opacity 1s ease-out'
      }, 500)
      break

    case 'updateConfig':
      // Allow runtime config updates
      if (data.config) {
        window.Config = { ...window.Config, ...data.config }
        initializePage()
      }
      break
  }
})

// Check if running in FiveM
const isFiveM = window.invokeNative !== undefined

// Disable context menu and F12 in FiveM
if (isFiveM) {
  document.addEventListener('contextmenu', (e) => e.preventDefault())
  document.addEventListener('keydown', (e) => {
    if (e.key === 'F12' || (e.ctrlKey && e.shiftKey && e.key === 'I')) {
      e.preventDefault()
    }
  })
}
