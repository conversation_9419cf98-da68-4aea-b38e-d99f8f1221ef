// Global variables
let currentSection = null
let progress = 0
let isPlaying = false
let isMuted = false
let currentTrackIndex = 0
let youtubePlayers = []
let currentPlayer = null
let isPlaylistOpen = false
let currentTime = 0
let duration = 0
let isYouTubeAPIReady = false

// Initialize page with config data
function initializePage() {
  if (!window.Config) {
    console.error('Config not loaded!')
    return
  }

  // Set footer text
  const footerText = document.getElementById('footerText')
  footerText.textContent = Config.general.copyright + ' | ' + Config.general.version

  // Initialize logo
  initializeLogo()

  // Initialize project team
  initializeProjectTeam()

  // Set background video
  if (Config.backgroundVideo.enabled) {
    const videoElement = document.getElementById('backgroundVideo')
    const videoParams = new URLSearchParams({
      autoplay: Config.backgroundVideo.autoplay ? '1' : '0',
      mute: Config.backgroundVideo.muted ? '1' : '0',
      loop: Config.backgroundVideo.loop ? '1' : '0',
      playlist: Config.backgroundVideo.youtubeId,
      controls: '0',
      showinfo: '0',
      rel: '0',
      iv_load_policy: '3',
      modestbranding: '1'
    })

    videoElement.src = `https://www.youtube.com/embed/${Config.backgroundVideo.youtubeId}?${videoParams.toString()}`
  }

  // Initialize music player
  if (Config.musicPlayer.enabled && Config.musicPlayer.tracks.length > 0) {
    if (isYouTubeAPIReady) {
      initializeYouTubePlayers()
    }
  }
}

// Initialize logo
function initializeLogo() {
  const logoImage = document.getElementById('logoImage')
  const logoTextFallback = document.getElementById('logoTextFallback')
  const logoInitials = document.getElementById('logoInitials')

  if (Config.logo.showImage && Config.logo.imageName) {
    // Logo'yu images klasöründen çek
    logoImage.src = `../images/${Config.logo.imageName}`
    logoImage.style.display = 'block'
    logoTextFallback.style.display = 'none'

    // Set logo size
    const logoContainer = document.getElementById('logoImageContainer')
    logoContainer.style.width = Config.logo.imageSize
    logoContainer.style.height = Config.logo.imageSize

    // Handle image load error
    logoImage.onerror = function() {
      console.log('Logo yüklenemedi, fallback kullanılıyor:', `../images/${Config.logo.imageName}`)
      logoImage.style.display = 'none'
      logoTextFallback.style.display = 'flex'
      logoInitials.textContent = Config.logo.title.charAt(0)
    }

    // Handle image load success
    logoImage.onload = function() {
      console.log('Logo başarıyla yüklendi:', `../images/${Config.logo.imageName}`)
    }
  } else {
    logoImage.style.display = 'none'
    logoTextFallback.style.display = 'flex'
    logoInitials.textContent = Config.logo.title.charAt(0)
  }
}

// Initialize project team
function initializeProjectTeam() {
  if (!Config.projectTeam.enabled || !Config.projectTeam.members.length) {
    document.getElementById('projectTeam').style.display = 'none'
    return
  }

  // Set team title
  document.getElementById('teamTitle').textContent = Config.projectTeam.title

  // Generate team members
  const teamMembers = document.getElementById('teamMembers')
  const membersHTML = Config.projectTeam.members.map(member => `
    <div class="team-member">
      <div class="team-avatar">
        <img src="../${member.avatar}" alt="${member.name}" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
        <div class="team-avatar-fallback" style="display: none;">
          ${member.name.charAt(0).toUpperCase()}
        </div>
      </div>
      <div class="team-info">
        <div class="team-name">${member.name}</div>
        <div class="team-role">${member.role}</div>
      </div>
    </div>
  `).join('')

  teamMembers.innerHTML = membersHTML
}

// YouTube API Ready
function onYouTubeIframeAPIReady() {
  console.log('YouTube API ready!')
  isYouTubeAPIReady = true

  if (window.Config && Config.musicPlayer.enabled && Config.musicPlayer.tracks.length > 0) {
    initializeYouTubePlayers()
  }
}

// Global function for YouTube API
window.onYouTubeIframeAPIReady = onYouTubeIframeAPIReady

// Initialize YouTube Players
function initializeYouTubePlayers() {
  console.log('Initializing YouTube players...')

  const container = document.getElementById('youtubePlayersContainer')

  // Create a player for each track
  Config.musicPlayer.tracks.forEach((track, index) => {
    const playerDiv = document.createElement('div')
    playerDiv.id = `youtubePlayer${index}`
    container.appendChild(playerDiv)

    try {
      const player = new YT.Player(`youtubePlayer${index}`, {
        height: '1',
        width: '1',
        videoId: track.youtubeId,
        playerVars: {
          autoplay: 0,
          controls: 0,
          disablekb: 1,
          fs: 0,
          modestbranding: 1,
          rel: 0,
          showinfo: 0,
          enablejsapi: 1,
          origin: window.location.origin,
          playsinline: 1
        },
        events: {
          onReady: (event) => onPlayerReady(event, index),
          onStateChange: (event) => onPlayerStateChange(event, index),
          onError: (event) => onPlayerError(event, index)
        }
      })

      youtubePlayers[index] = player
      console.log(`Player ${index} created for video: ${track.youtubeId}`)
    } catch (error) {
      console.error(`Error creating player ${index}:`, error)
    }
  })

  // Set current player
  currentPlayer = youtubePlayers[0]

  // Initialize UI
  initializeMusicPlayer()
  generatePlaylist()

  // Load track info
  loadTrackInfo()
}

// Player ready event
function onPlayerReady(event, index) {
  console.log(`YouTube player ${index} ready!`)

  // Set volume for all players
  event.target.setVolume(Config.musicPlayer.volume * 100)

  if (index === 0) {
    // Auto play if enabled (only for first player)
    if (Config.musicPlayer.autoplay) {
      setTimeout(() => {
        console.log('Starting autoplay...')
        try {
          event.target.playVideo()
          console.log('Autoplay command sent successfully')
        } catch (error) {
          console.error('Autoplay failed:', error)
        }
      }, 3000) // 3 saniye bekle
    }

    // Start time update interval
    setInterval(updateProgress, 1000)
  }
}

// Player state change event
function onPlayerStateChange(event, index) {
  if (index !== currentTrackIndex) return

  if (event.data === YT.PlayerState.PLAYING) {
    isPlaying = true
    updatePlayButton()

    // Update duration when video starts playing
    setTimeout(() => {
      const videoDuration = event.target.getDuration()
      if (videoDuration > 0) {
        duration = videoDuration
        document.getElementById('totalTime').textContent = formatTime(videoDuration)
      }
    }, 1000)

  } else if (event.data === YT.PlayerState.PAUSED) {
    isPlaying = false
    updatePlayButton()
  } else if (event.data === YT.PlayerState.ENDED) {
    nextTrack()
  }
}

// Player error event
function onPlayerError(event, index) {
  console.error(`YouTube player ${index} error:`, event.data)
  if (index === currentTrackIndex) {
    // Try next track on error
    if (Config.musicPlayer.tracks.length > 1) {
      setTimeout(() => {
        nextTrack()
      }, 2000)
    }
  }
}

// Initialize music player UI
function initializeMusicPlayer() {
  if (!Config.musicPlayer.enabled || !Config.musicPlayer.tracks.length) {
    document.getElementById('musicPlayer').style.display = 'none'
    return
  }

  loadTrackInfo()
}

// Load track info
function loadTrackInfo() {
  const track = Config.musicPlayer.tracks[currentTrackIndex]

  // Set track name and artist
  document.getElementById('trackName').textContent = track.name || `Track ${currentTrackIndex + 1}`
  document.getElementById('trackArtist').textContent = track.artist || 'YouTube'
  document.getElementById('totalTime').textContent = '0:00'

  // Update thumbnail from YouTube
  const thumbnail = document.getElementById('trackThumbnail')
  const defaultIcon = document.getElementById('defaultIcon')

  if (Config.musicPlayer.showThumbnails && track.youtubeId) {
    // Use YouTube thumbnail
    thumbnail.src = `https://img.youtube.com/vi/${track.youtubeId}/maxresdefault.jpg`
    thumbnail.style.display = 'block'
    defaultIcon.style.display = 'none'

    // Handle thumbnail error - try different quality
    thumbnail.onerror = function() {
      thumbnail.src = `https://img.youtube.com/vi/${track.youtubeId}/hqdefault.jpg`
      thumbnail.onerror = function() {
        thumbnail.style.display = 'none'
        defaultIcon.style.display = 'block'
      }
    }
  } else {
    thumbnail.style.display = 'none'
    defaultIcon.style.display = 'block'
  }
}

// Initialize loading progress
function initializeLoading() {
  const loadingSteps = Config.loadingSteps
  const loadingSpeed = Config.general.loadingSpeed

  const interval = setInterval(() => {
    progress += Math.random() * 2

    if (progress >= 100) {
      progress = 100
      clearInterval(interval)
    }

    // Update progress bar (eski loading bar)
    const progressFill = document.getElementById("progressFill")
    const progressText = document.getElementById("progressText")
    if (progressFill) progressFill.style.width = progress + "%"
    if (progressText) progressText.textContent = Math.round(progress) + "%"

    // Update loading circle
    updateLoadingCircle(progress)

    // Update loading percentage
    const loadingPercentage = document.getElementById("loadingPercentage")
    if (loadingPercentage) loadingPercentage.textContent = Math.round(progress) + "%"

    // Update loading step
    const stepIndex = Math.floor((progress / 100) * loadingSteps.length)
    const currentStep = loadingSteps[Math.min(stepIndex, loadingSteps.length - 1)]
    const loadingStep = document.getElementById("loadingStep")
    if (loadingStep) loadingStep.textContent = currentStep
  }, loadingSpeed)
}

// Update loading circle
function updateLoadingCircle(progressValue) {
  const circle = document.getElementById('loadingCircleProgress')
  if (circle) {
    const circumference = 2 * Math.PI * 45 // r=45
    const offset = circumference - (progressValue / 100) * circumference
    circle.style.strokeDashoffset = offset
  }
}

// Toggle section
function toggleSection(section) {
  const panel = document.getElementById("sidePanel")
  const chevron = document.getElementById(section + "-chevron")

  if (currentSection === section) {
    // Close current section
    closePanel()
    return
  }

  // Reset all chevrons
  document.querySelectorAll(".menu-chevron").forEach((ch) => {
    ch.classList.remove("rotated")
    ch.classList.remove("fa-chevron-left")
    ch.classList.add("fa-chevron-right")
  })

  // Update current chevron
  chevron.classList.add("rotated")
  chevron.classList.remove("fa-chevron-right")
  chevron.classList.add("fa-chevron-left")

  currentSection = section
  loadPanelContent(section)
  panel.classList.add("open")
}

// Close panel
function closePanel() {
  const panel = document.getElementById("sidePanel")
  panel.classList.remove("open")
  currentSection = null

  // Reset all chevrons
  document.querySelectorAll(".menu-chevron").forEach((chevron) => {
    chevron.classList.remove("rotated")
    chevron.classList.remove("fa-chevron-left")
    chevron.classList.add("fa-chevron-right")
  })
}

// Load panel content
function loadPanelContent(section) {
  const titleElement = document.getElementById("panelTitle")
  const contentElement = document.getElementById("panelContent")

  let title = ""
  let content = ""

  switch (section) {
    case "announcements":
      title = "Duyurular"
      content = generateAnnouncementsContent()
      break
    case "rules":
      title = "Sunucu Kuralları"
      content = generateRulesContent()
      break
    case "social":
      title = "Topluluk"
      content = generateSocialContent()
      break
  }

  titleElement.textContent = title
  contentElement.innerHTML = content
}

// Generate announcements content
function generateAnnouncementsContent() {
  return Config.announcements
    .map(
      (announcement) => `
        <div class="announcement-item">
            <div class="announcement-header">
                <i class="${announcement.icon} announcement-icon ${announcement.color}"></i>
                <div class="announcement-content">
                    <h4 class="announcement-title">${announcement.title}</h4>
                    <p class="announcement-text">${announcement.content}</p>
                    <p class="announcement-date">${announcement.date}</p>
                </div>
            </div>
        </div>
    `,
    )
    .join("")
}

// Generate rules content
function generateRulesContent() {
  return Config.serverRules
    .map(
      (rule, index) => `
        <div class="rule-item">
            <div class="rule-number">
                <span>${index + 1}</span>
            </div>
            <p class="rule-text">${rule}</p>
        </div>
    `,
    )
    .join("")
}

// Generate social content
function generateSocialContent() {
  return Config.socialLinks
    .map(
      (link) => `
        <a href="${link.url}" target="_blank" class="social-btn ${link.class}">
            <i class="${link.icon}"></i>
            <span>${link.name}</span>
        </a>
    `,
    )
    .join("")
}

// Update progress
function updateProgress() {
  if (!currentPlayer || !currentPlayer.getCurrentTime) return

  try {
    currentTime = currentPlayer.getCurrentTime()
    duration = currentPlayer.getDuration()

    if (duration > 0) {
      const progressPercent = (currentTime / duration) * 100
      document.getElementById('progressTrackFill').style.width = progressPercent + '%'
      document.getElementById('currentTime').textContent = formatTime(currentTime)
    }
  } catch (error) {
    // Ignore errors during progress update
  }
}

// Format time
function formatTime(seconds) {
  const mins = Math.floor(seconds / 60)
  const secs = Math.floor(seconds % 60)
  return `${mins}:${secs.toString().padStart(2, '0')}`
}

// Update play button
function updatePlayButton() {
  const playIcon = document.getElementById("playIcon")
  if (isPlaying) {
    playIcon.classList.remove("fa-play")
    playIcon.classList.add("fa-pause")
  } else {
    playIcon.classList.remove("fa-pause")
    playIcon.classList.add("fa-play")
  }
}

// Music player functions
function togglePlay() {
  if (!currentPlayer) {
    console.log('YouTube player not ready')
    return
  }

  try {
    // Check if player is ready
    if (typeof currentPlayer.getPlayerState !== 'function') {
      console.log('Player not fully initialized yet')
      return
    }

    const playerState = currentPlayer.getPlayerState()
    console.log('Current player state:', playerState)

    if (isPlaying || playerState === YT.PlayerState.PLAYING) {
      console.log('Pausing video...')
      currentPlayer.pauseVideo()
    } else {
      console.log('Playing video...')
      currentPlayer.playVideo()
    }
  } catch (error) {
    console.error('Error toggling play:', error)
  }
}

function toggleMute() {
  if (!currentPlayer) return

  isMuted = !isMuted
  const muteIcon = document.getElementById("muteIcon")

  if (isMuted) {
    muteIcon.classList.remove("fa-volume-up")
    muteIcon.classList.add("fa-volume-mute")
    currentPlayer.setVolume(0)
  } else {
    muteIcon.classList.remove("fa-volume-mute")
    muteIcon.classList.add("fa-volume-up")
    currentPlayer.setVolume(Config.musicPlayer.volume * 100)
  }
}

function previousTrack() {
  if (!Config.musicPlayer.enabled || !Config.musicPlayer.tracks.length) return

  // Pause current player
  if (currentPlayer) {
    currentPlayer.pauseVideo()
  }

  currentTrackIndex = currentTrackIndex > 0 ? currentTrackIndex - 1 : Config.musicPlayer.tracks.length - 1
  loadTrack()
}

function nextTrack() {
  if (!Config.musicPlayer.enabled || !Config.musicPlayer.tracks.length) return

  // Pause current player
  if (currentPlayer) {
    currentPlayer.pauseVideo()
  }

  currentTrackIndex = currentTrackIndex < Config.musicPlayer.tracks.length - 1 ? currentTrackIndex + 1 : 0
  loadTrack()
}

function loadTrack() {
  if (!youtubePlayers[currentTrackIndex]) {
    console.log('YouTube player not ready for track loading')
    return
  }

  try {
    // Switch to new player
    currentPlayer = youtubePlayers[currentTrackIndex]

    console.log('Loading track:', Config.musicPlayer.tracks[currentTrackIndex].youtubeId)

    // Set volume
    currentPlayer.setVolume(isMuted ? 0 : Config.musicPlayer.volume * 100)

    // Load track info
    loadTrackInfo()
    updatePlaylistSelection()

    // Auto play if was playing
    if (isPlaying) {
      setTimeout(() => {
        currentPlayer.playVideo()
      }, 500)
    }
  } catch (error) {
    console.error('Error loading track:', error)
  }
}

function togglePlaylist() {
  isPlaylistOpen = !isPlaylistOpen
  const playlist = document.getElementById('playlist')

  if (isPlaylistOpen) {
    playlist.classList.add('open')
  } else {
    playlist.classList.remove('open')
  }
}

function generatePlaylist() {
  const playlistContent = document.getElementById('playlistContent')

  const playlistHTML = Config.musicPlayer.tracks.map((track, index) => `
    <div class="playlist-item ${index === currentTrackIndex ? 'active' : ''}" onclick="selectTrack(${index})">
      <div class="playlist-item-thumbnail">
        <img src="https://img.youtube.com/vi/${track.youtubeId}/hqdefault.jpg" alt="${track.name || 'Track'}" onerror="this.style.display='none'">
      </div>
      <div class="playlist-item-info">
        <div class="playlist-item-name">${track.name || `Track ${index + 1}`}</div>
        <div class="playlist-item-artist">${track.artist || 'YouTube'}</div>
      </div>
      <div class="playlist-item-duration">--:--</div>
    </div>
  `).join('')

  playlistContent.innerHTML = playlistHTML
}

function selectTrack(index) {
  currentTrackIndex = index
  loadTrack()
  togglePlaylist()
}

function updatePlaylistSelection() {
  const items = document.querySelectorAll('.playlist-item')
  items.forEach((item, index) => {
    if (index === currentTrackIndex) {
      item.classList.add('active')
    } else {
      item.classList.remove('active')
    }
  })
}

// Progress bar click handler
function setupProgressBarClick() {
  const progressTrack = document.getElementById('progressTrack')
  if (progressTrack) {
    progressTrack.addEventListener('click', (e) => {
      if (!currentPlayer || !duration) return

      const rect = progressTrack.getBoundingClientRect()
      const clickX = e.clientX - rect.left
      const percentage = clickX / rect.width
      const seekTime = duration * percentage

      try {
        currentPlayer.seekTo(seekTime)
      } catch (error) {
        console.error('Seek failed:', error)
      }
    })
  }
}

// Initialize when page loads
document.addEventListener("DOMContentLoaded", () => {
  console.log('DOM loaded, initializing...')

  // Wait for config to load
  if (window.Config) {
    console.log('Config found, initializing page...')
    initializePage()
    initializeLoading()
    setupProgressBarClick()

    // Initialize YouTube players if music is enabled
    if (Config.musicPlayer.enabled && Config.musicPlayer.tracks.length > 0) {
      if (isYouTubeAPIReady) {
        console.log('YouTube API ready, initializing players...')
        initializeYouTubePlayers()
      } else {
        console.log('Waiting for YouTube API...')
      }
    }
  } else {
    console.log('Config not found, retrying...')
    // Retry after a short delay if config not loaded
    setTimeout(() => {
      if (window.Config) {
        console.log('Config found on retry, initializing...')
        initializePage()
        initializeLoading()
        setupProgressBarClick()
      } else {
        console.error('Config still not found after retry')
      }
    }, 500)
  }
})

// Handle escape key to close panel
document.addEventListener("keydown", (event) => {
  if (event.key === "Escape" && currentSection && Config.general.enableEscapeKey) {
    closePanel()
  }
})

// FiveM NUI Message Handler
window.addEventListener('message', (event) => {
  const data = event.data

  switch(data.type) {
    case 'loadProgress':
      // Update progress from client.lua
      progress = data.progress

      // Update old progress bar (if exists)
      const progressFill = document.getElementById("progressFill")
      const progressText = document.getElementById("progressText")
      if (progressFill) progressFill.style.width = progress + "%"
      if (progressText) progressText.textContent = Math.round(progress) + "%"

      // Update loading circle
      updateLoadingCircle(progress)

      // Update loading percentage
      const loadingPercentage = document.getElementById("loadingPercentage")
      if (loadingPercentage) loadingPercentage.textContent = Math.round(progress) + "%"

      // Update loading step if provided
      if (data.step) {
        const loadingStep = document.getElementById("loadingStep")
        if (loadingStep) loadingStep.textContent = data.step
      }
      break

    case 'loadingComplete':
      // Loading completed, prepare for fade out
      setTimeout(() => {
        document.body.style.opacity = '0'
        document.body.style.transition = 'opacity 1s ease-out'
      }, 500)
      break

    case 'updateConfig':
      // Allow runtime config updates
      if (data.config) {
        window.Config = { ...window.Config, ...data.config }
        initializePage()
      }
      break
  }
})

// Check if running in FiveM
const isFiveM = window.invokeNative !== undefined

// Disable context menu and F12 in FiveM
if (isFiveM) {
  document.addEventListener('contextmenu', (e) => e.preventDefault())
  document.addEventListener('keydown', (e) => {
    if (e.key === 'F12' || (e.ctrlKey && e.shiftKey && e.key === 'I')) {
      e.preventDefault()
    }
  })
}
