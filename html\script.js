// Global variables
let currentSection = null
let progress = 0
let isPlaying = false
let isMuted = false
let currentTrackIndex = 0
let audio = null

// Initialize page with config data
function initializePage() {
  if (!window.Config) {
    console.error('Config not loaded!')
    return
  }

  // Set logo and title
  const logoTitle = document.getElementById('logoTitle')
  const logoSubtitle = document.getElementById('logoSubtitle')
  const footerText = document.getElementById('footerText')

  logoTitle.innerHTML = Config.logo.title + '<span class="logo-accent">' + Config.logo.titleAccent + '</span>'
  logoSubtitle.textContent = Config.logo.subtitle
  footerText.textContent = Config.general.copyright + ' | ' + Config.general.version

  // Set background video
  if (Config.backgroundVideo.enabled) {
    const videoElement = document.getElementById('backgroundVideo')
    const videoParams = new URLSearchParams({
      autoplay: Config.backgroundVideo.autoplay ? '1' : '0',
      mute: Config.backgroundVideo.muted ? '1' : '0',
      loop: Config.backgroundVideo.loop ? '1' : '0',
      playlist: Config.backgroundVideo.youtubeId,
      controls: '0',
      showinfo: '0',
      rel: '0',
      iv_load_policy: '3',
      modestbranding: '1'
    })

    videoElement.src = `https://www.youtube.com/embed/${Config.backgroundVideo.youtubeId}?${videoParams.toString()}`
  }

  // Initialize music player
  if (Config.musicPlayer.enabled && Config.musicPlayer.tracks.length > 0) {
    initializeMusicPlayer()
  }
}

// Initialize music player
function initializeMusicPlayer() {
  const currentTrack = Config.musicPlayer.tracks[currentTrackIndex]
  document.getElementById('trackName').textContent = currentTrack.name
  document.getElementById('totalTime').textContent = currentTrack.duration

  // Create audio element if enabled
  if (Config.musicPlayer.autoplay) {
    audio = new Audio(currentTrack.url)
    audio.volume = Config.musicPlayer.volume
  }
}

// Initialize loading progress
function initializeLoading() {
  const loadingSteps = Config.loadingSteps
  const loadingSpeed = Config.general.loadingSpeed

  const interval = setInterval(() => {
    progress += Math.random() * 2

    if (progress >= 100) {
      progress = 100
      clearInterval(interval)
    }

    // Update progress bar
    document.getElementById("progressFill").style.width = progress + "%"
    document.getElementById("progressText").textContent = Math.round(progress) + "%"

    // Update loading step
    const stepIndex = Math.floor((progress / 100) * loadingSteps.length)
    const currentStep = loadingSteps[Math.min(stepIndex, loadingSteps.length - 1)]
    document.getElementById("loadingStep").textContent = currentStep
  }, loadingSpeed)
}

// Toggle section
function toggleSection(section) {
  const panel = document.getElementById("sidePanel")
  const chevron = document.getElementById(section + "-chevron")

  if (currentSection === section) {
    // Close current section
    closePanel()
    return
  }

  // Reset all chevrons
  document.querySelectorAll(".menu-chevron").forEach((ch) => {
    ch.classList.remove("rotated")
    ch.classList.remove("fa-chevron-left")
    ch.classList.add("fa-chevron-right")
  })

  // Update current chevron
  chevron.classList.add("rotated")
  chevron.classList.remove("fa-chevron-right")
  chevron.classList.add("fa-chevron-left")

  currentSection = section
  loadPanelContent(section)
  panel.classList.add("open")
}

// Close panel
function closePanel() {
  const panel = document.getElementById("sidePanel")
  panel.classList.remove("open")
  currentSection = null

  // Reset all chevrons
  document.querySelectorAll(".menu-chevron").forEach((chevron) => {
    chevron.classList.remove("rotated")
    chevron.classList.remove("fa-chevron-left")
    chevron.classList.add("fa-chevron-right")
  })
}

// Load panel content
function loadPanelContent(section) {
  const titleElement = document.getElementById("panelTitle")
  const contentElement = document.getElementById("panelContent")

  let title = ""
  let content = ""

  switch (section) {
    case "announcements":
      title = "Duyurular"
      content = generateAnnouncementsContent()
      break
    case "rules":
      title = "Sunucu Kuralları"
      content = generateRulesContent()
      break
    case "social":
      title = "Topluluk"
      content = generateSocialContent()
      break
  }

  titleElement.textContent = title
  contentElement.innerHTML = content
}

// Generate announcements content
function generateAnnouncementsContent() {
  return Config.announcements
    .map(
      (announcement) => `
        <div class="announcement-item">
            <div class="announcement-header">
                <i class="${announcement.icon} announcement-icon ${announcement.color}"></i>
                <div class="announcement-content">
                    <h4 class="announcement-title">${announcement.title}</h4>
                    <p class="announcement-text">${announcement.content}</p>
                    <p class="announcement-date">${announcement.date}</p>
                </div>
            </div>
        </div>
    `,
    )
    .join("")
}

// Generate rules content
function generateRulesContent() {
  return Config.serverRules
    .map(
      (rule, index) => `
        <div class="rule-item">
            <div class="rule-number">
                <span>${index + 1}</span>
            </div>
            <p class="rule-text">${rule}</p>
        </div>
    `,
    )
    .join("")
}

// Generate social content
function generateSocialContent() {
  return Config.socialLinks
    .map(
      (link) => `
        <a href="${link.url}" target="_blank" class="social-btn ${link.class}">
            <i class="${link.icon}"></i>
            <span>${link.name}</span>
        </a>
    `,
    )
    .join("")
}

// Music player functions
function togglePlay() {
  if (!Config.musicPlayer.enabled || !Config.musicPlayer.tracks.length) return

  isPlaying = !isPlaying
  const playIcon = document.getElementById("playIcon")

  if (isPlaying) {
    playIcon.classList.remove("fa-play")
    playIcon.classList.add("fa-pause")
    if (audio) {
      audio.play().catch(e => console.log('Audio play failed:', e))
    }
  } else {
    playIcon.classList.remove("fa-pause")
    playIcon.classList.add("fa-play")
    if (audio) {
      audio.pause()
    }
  }
}

function toggleMute() {
  isMuted = !isMuted
  const muteIcon = document.getElementById("muteIcon")

  if (isMuted) {
    muteIcon.classList.remove("fa-volume-up")
    muteIcon.classList.add("fa-volume-mute")
    if (audio) audio.volume = 0
  } else {
    muteIcon.classList.remove("fa-volume-mute")
    muteIcon.classList.add("fa-volume-up")
    if (audio) audio.volume = Config.musicPlayer.volume
  }
}

function previousTrack() {
  if (!Config.musicPlayer.enabled || !Config.musicPlayer.tracks.length) return

  currentTrackIndex = currentTrackIndex > 0 ? currentTrackIndex - 1 : Config.musicPlayer.tracks.length - 1
  loadTrack()
}

function nextTrack() {
  if (!Config.musicPlayer.enabled || !Config.musicPlayer.tracks.length) return

  currentTrackIndex = currentTrackIndex < Config.musicPlayer.tracks.length - 1 ? currentTrackIndex + 1 : 0
  loadTrack()
}

function loadTrack() {
  const track = Config.musicPlayer.tracks[currentTrackIndex]
  document.getElementById('trackName').textContent = track.name
  document.getElementById('totalTime').textContent = track.duration

  if (audio) {
    audio.src = track.url
    if (isPlaying) {
      audio.play().catch(e => console.log('Audio play failed:', e))
    }
  }
}

// Initialize when page loads
document.addEventListener("DOMContentLoaded", () => {
  // Wait for config to load
  if (window.Config) {
    initializePage()
    initializeLoading()
  } else {
    // Retry after a short delay if config not loaded
    setTimeout(() => {
      if (window.Config) {
        initializePage()
        initializeLoading()
      }
    }, 100)
  }
})

// Handle escape key to close panel
document.addEventListener("keydown", (event) => {
  if (event.key === "Escape" && currentSection && Config.general.enableEscapeKey) {
    closePanel()
  }
})

// FiveM specific functions
if (window.invokeNative) {
  // Hide loading screen when game is ready
  window.addEventListener('message', (event) => {
    if (event.data.type === 'loadProgress') {
      // Update progress from FiveM
      progress = event.data.progress
      document.getElementById("progressFill").style.width = progress + "%"
      document.getElementById("progressText").textContent = Math.round(progress) + "%"
    }
  })
}
