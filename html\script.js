// Global variables
let currentSection = null
let progress = 0
let isPlaying = false
let isMuted = false
let currentTrackIndex = 0
let audioPlayer = null
let isPlaylistOpen = false
let currentTime = 0
let duration = 0

// Initialize page with config data
function initializePage() {
  if (!window.Config) {
    console.error('Config not loaded!')
    return
  }

  // Set footer text
  const footerText = document.getElementById('footerText')
  footerText.textContent = Config.general.copyright + ' | ' + Config.general.version

  // Initialize logo
  initializeLogo()

  // Initialize project team
  initializeProjectTeam()

  // Set background video
  if (Config.backgroundVideo.enabled) {
    const videoElement = document.getElementById('backgroundVideo')
    const videoParams = new URLSearchParams({
      autoplay: Config.backgroundVideo.autoplay ? '1' : '0',
      mute: Config.backgroundVideo.muted ? '1' : '0',
      loop: Config.backgroundVideo.loop ? '1' : '0',
      playlist: Config.backgroundVideo.youtubeId,
      controls: '0',
      showinfo: '0',
      rel: '0',
      iv_load_policy: '3',
      modestbranding: '1'
    })

    videoElement.src = `https://www.youtube.com/embed/${Config.backgroundVideo.youtubeId}?${videoParams.toString()}`
  }

  // Initialize music player
  if (Config.musicPlayer.enabled && Config.musicPlayer.tracks.length > 0) {
    initializeAudioPlayer()
  }
}

// Initialize logo
function initializeLogo() {
  const logoImage = document.getElementById('logoImage')
  const logoTextFallback = document.getElementById('logoTextFallback')
  const logoInitials = document.getElementById('logoInitials')

  if (Config.logo.showImage && Config.logo.imageName) {
    // Logo'yu images klasöründen çek
    logoImage.src = `../images/${Config.logo.imageName}`
    logoImage.style.display = 'block'
    logoTextFallback.style.display = 'none'

    // Set logo size
    const logoContainer = document.getElementById('logoImageContainer')
    logoContainer.style.width = Config.logo.imageSize
    logoContainer.style.height = Config.logo.imageSize

    // Handle image load error
    logoImage.onerror = function() {
      console.log('Logo yüklenemedi, fallback kullanılıyor:', `../images/${Config.logo.imageName}`)
      logoImage.style.display = 'none'
      logoTextFallback.style.display = 'flex'
      logoInitials.textContent = Config.logo.title.charAt(0)
    }

    // Handle image load success
    logoImage.onload = function() {
      console.log('Logo başarıyla yüklendi:', `../images/${Config.logo.imageName}`)
    }
  } else {
    logoImage.style.display = 'none'
    logoTextFallback.style.display = 'flex'
    logoInitials.textContent = Config.logo.title.charAt(0)
  }
}

// Initialize project team
function initializeProjectTeam() {
  if (!Config.projectTeam.enabled || !Config.projectTeam.members.length) {
    document.getElementById('projectTeam').style.display = 'none'
    return
  }

  // Set team title
  document.getElementById('teamTitle').textContent = Config.projectTeam.title

  // Generate team members
  const teamMembers = document.getElementById('teamMembers')
  const membersHTML = Config.projectTeam.members.map(member => `
    <div class="team-member">
      <div class="team-avatar">
        <img src="../${member.avatar}" alt="${member.name}" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
        <div class="team-avatar-fallback" style="display: none;">
          ${member.name.charAt(0).toUpperCase()}
        </div>
      </div>
      <div class="team-info">
        <div class="team-name">${member.name}</div>
        <div class="team-role">${member.role}</div>
      </div>
    </div>
  `).join('')

  teamMembers.innerHTML = membersHTML
}

// Initialize Audio Player
function initializeAudioPlayer() {
  console.log('Initializing audio player...')

  // Create audio element
  audioPlayer = new Audio()
  audioPlayer.volume = Config.musicPlayer.volume
  audioPlayer.preload = 'metadata'

  // Audio event listeners
  audioPlayer.addEventListener('loadedmetadata', () => {
    duration = audioPlayer.duration
    document.getElementById('totalTime').textContent = formatTime(duration)
  })

  audioPlayer.addEventListener('timeupdate', updateProgress)

  audioPlayer.addEventListener('play', () => {
    isPlaying = true
    updatePlayButton()
  })

  audioPlayer.addEventListener('pause', () => {
    isPlaying = false
    updatePlayButton()
  })

  audioPlayer.addEventListener('ended', () => {
    nextTrack()
  })

  audioPlayer.addEventListener('error', (e) => {
    console.error('Audio error:', e)
    // Try next track on error
    if (Config.musicPlayer.tracks.length > 1) {
      setTimeout(() => {
        nextTrack()
      }, 1000)
    }
  })

  // Initialize UI
  initializeMusicPlayer()
  generatePlaylist()

  // Load first track
  loadTrack()

  // Auto play if enabled
  if (Config.musicPlayer.autoplay) {
    setTimeout(() => {
      console.log('Starting autoplay...')
      audioPlayer.play().catch(e => {
        console.log('Autoplay prevented by browser:', e)
      })
    }, 1000)
  }
}

// Initialize music player UI
function initializeMusicPlayer() {
  if (!Config.musicPlayer.enabled || !Config.musicPlayer.tracks.length) {
    document.getElementById('musicPlayer').style.display = 'none'
    return
  }

  loadTrackInfo()
}

// Load track info
function loadTrackInfo() {
  const track = Config.musicPlayer.tracks[currentTrackIndex]

  // Set track name and artist
  document.getElementById('trackName').textContent = track.name || `Track ${currentTrackIndex + 1}`
  document.getElementById('trackArtist').textContent = track.artist || 'Unknown Artist'
  document.getElementById('totalTime').textContent = track.duration || '0:00'

  // Hide thumbnail (no thumbnails for file-based music)
  const thumbnail = document.getElementById('trackThumbnail')
  const defaultIcon = document.getElementById('defaultIcon')

  thumbnail.style.display = 'none'
  defaultIcon.style.display = 'block'
}

// Initialize loading progress
function initializeLoading() {
  const loadingSteps = Config.loadingSteps
  const loadingSpeed = Config.general.loadingSpeed

  const interval = setInterval(() => {
    progress += Math.random() * 2

    if (progress >= 100) {
      progress = 100
      clearInterval(interval)
    }

    // Update progress bar (eski loading bar)
    const progressFill = document.getElementById("progressFill")
    const progressText = document.getElementById("progressText")
    if (progressFill) progressFill.style.width = progress + "%"
    if (progressText) progressText.textContent = Math.round(progress) + "%"

    // Update loading circle
    updateLoadingCircle(progress)

    // Update loading percentage
    const loadingPercentage = document.getElementById("loadingPercentage")
    if (loadingPercentage) loadingPercentage.textContent = Math.round(progress) + "%"

    // Update loading step
    const stepIndex = Math.floor((progress / 100) * loadingSteps.length)
    const currentStep = loadingSteps[Math.min(stepIndex, loadingSteps.length - 1)]
    const loadingStep = document.getElementById("loadingStep")
    if (loadingStep) loadingStep.textContent = currentStep
  }, loadingSpeed)
}

// Update loading circle
function updateLoadingCircle(progressValue) {
  const circle = document.getElementById('loadingCircleProgress')
  if (circle) {
    const circumference = 2 * Math.PI * 45 // r=45
    const offset = circumference - (progressValue / 100) * circumference
    circle.style.strokeDashoffset = offset
  }
}

// Toggle section
function toggleSection(section) {
  const panel = document.getElementById("sidePanel")
  const chevron = document.getElementById(section + "-chevron")

  if (currentSection === section) {
    // Close current section
    closePanel()
    return
  }

  // Reset all chevrons
  document.querySelectorAll(".menu-chevron").forEach((ch) => {
    ch.classList.remove("rotated")
    ch.classList.remove("fa-chevron-left")
    ch.classList.add("fa-chevron-right")
  })

  // Update current chevron
  chevron.classList.add("rotated")
  chevron.classList.remove("fa-chevron-right")
  chevron.classList.add("fa-chevron-left")

  currentSection = section
  loadPanelContent(section)
  panel.classList.add("open")
}

// Close panel
function closePanel() {
  const panel = document.getElementById("sidePanel")
  panel.classList.remove("open")
  currentSection = null

  // Reset all chevrons
  document.querySelectorAll(".menu-chevron").forEach((chevron) => {
    chevron.classList.remove("rotated")
    chevron.classList.remove("fa-chevron-left")
    chevron.classList.add("fa-chevron-right")
  })
}

// Load panel content
function loadPanelContent(section) {
  const titleElement = document.getElementById("panelTitle")
  const contentElement = document.getElementById("panelContent")

  let title = ""
  let content = ""

  switch (section) {
    case "announcements":
      title = "Duyurular"
      content = generateAnnouncementsContent()
      break
    case "rules":
      title = "Sunucu Kuralları"
      content = generateRulesContent()
      break
    case "social":
      title = "Topluluk"
      content = generateSocialContent()
      break
  }

  titleElement.textContent = title
  contentElement.innerHTML = content
}

// Generate announcements content
function generateAnnouncementsContent() {
  return Config.announcements
    .map(
      (announcement) => `
        <div class="announcement-item">
            <div class="announcement-header">
                <i class="${announcement.icon} announcement-icon ${announcement.color}"></i>
                <div class="announcement-content">
                    <h4 class="announcement-title">${announcement.title}</h4>
                    <p class="announcement-text">${announcement.content}</p>
                    <p class="announcement-date">${announcement.date}</p>
                </div>
            </div>
        </div>
    `,
    )
    .join("")
}

// Generate rules content
function generateRulesContent() {
  return Config.serverRules
    .map(
      (rule, index) => `
        <div class="rule-item">
            <div class="rule-number">
                <span>${index + 1}</span>
            </div>
            <p class="rule-text">${rule}</p>
        </div>
    `,
    )
    .join("")
}

// Generate social content
function generateSocialContent() {
  return Config.socialLinks
    .map(
      (link) => `
        <a href="${link.url}" target="_blank" class="social-btn ${link.class}">
            <i class="${link.icon}"></i>
            <span>${link.name}</span>
        </a>
    `,
    )
    .join("")
}

// Update progress
function updateProgress() {
  if (!audioPlayer) return

  currentTime = audioPlayer.currentTime
  duration = audioPlayer.duration

  if (duration > 0) {
    const progressPercent = (currentTime / duration) * 100
    document.getElementById('progressTrackFill').style.width = progressPercent + '%'
    document.getElementById('currentTime').textContent = formatTime(currentTime)
  }
}

// Format time
function formatTime(seconds) {
  const mins = Math.floor(seconds / 60)
  const secs = Math.floor(seconds % 60)
  return `${mins}:${secs.toString().padStart(2, '0')}`
}

// Update play button
function updatePlayButton() {
  const playIcon = document.getElementById("playIcon")
  if (isPlaying) {
    playIcon.classList.remove("fa-play")
    playIcon.classList.add("fa-pause")
  } else {
    playIcon.classList.remove("fa-pause")
    playIcon.classList.add("fa-play")
  }
}

// Music player functions
function togglePlay() {
  if (!audioPlayer) {
    console.log('Audio player not ready')
    return
  }

  try {
    if (isPlaying) {
      console.log('Pausing audio...')
      audioPlayer.pause()
    } else {
      console.log('Playing audio...')
      audioPlayer.play().catch(e => {
        console.error('Play failed:', e)
      })
    }
  } catch (error) {
    console.error('Error toggling play:', error)
  }
}

function toggleMute() {
  if (!audioPlayer) return

  isMuted = !isMuted
  const muteIcon = document.getElementById("muteIcon")

  if (isMuted) {
    muteIcon.classList.remove("fa-volume-up")
    muteIcon.classList.add("fa-volume-mute")
    audioPlayer.volume = 0
  } else {
    muteIcon.classList.remove("fa-volume-mute")
    muteIcon.classList.add("fa-volume-up")
    audioPlayer.volume = Config.musicPlayer.volume
  }
}

function previousTrack() {
  if (!Config.musicPlayer.enabled || !Config.musicPlayer.tracks.length) return

  currentTrackIndex = currentTrackIndex > 0 ? currentTrackIndex - 1 : Config.musicPlayer.tracks.length - 1
  loadTrack()
}

function nextTrack() {
  if (!Config.musicPlayer.enabled || !Config.musicPlayer.tracks.length) return

  currentTrackIndex = currentTrackIndex < Config.musicPlayer.tracks.length - 1 ? currentTrackIndex + 1 : 0
  loadTrack()
}

function loadTrack() {
  if (!audioPlayer) {
    console.log('Audio player not ready for track loading')
    return
  }

  try {
    const track = Config.musicPlayer.tracks[currentTrackIndex]
    console.log('Loading track:', track.url)
    audioPlayer.src = `../${track.url}`
    audioPlayer.load()
    loadTrackInfo()
    updatePlaylistSelection()
  } catch (error) {
    console.error('Error loading track:', error)
  }
}

function togglePlaylist() {
  isPlaylistOpen = !isPlaylistOpen
  const playlist = document.getElementById('playlist')

  if (isPlaylistOpen) {
    playlist.classList.add('open')
  } else {
    playlist.classList.remove('open')
  }
}

function generatePlaylist() {
  const playlistContent = document.getElementById('playlistContent')

  const playlistHTML = Config.musicPlayer.tracks.map((track, index) => `
    <div class="playlist-item ${index === currentTrackIndex ? 'active' : ''}" onclick="selectTrack(${index})">
      <div class="playlist-item-thumbnail">
        <i class="fas fa-music"></i>
      </div>
      <div class="playlist-item-info">
        <div class="playlist-item-name">${track.name}</div>
        <div class="playlist-item-artist">${track.artist}</div>
      </div>
      <div class="playlist-item-duration">${track.duration}</div>
    </div>
  `).join('')

  playlistContent.innerHTML = playlistHTML
}

function selectTrack(index) {
  currentTrackIndex = index
  loadTrack()
  togglePlaylist()
}

function updatePlaylistSelection() {
  const items = document.querySelectorAll('.playlist-item')
  items.forEach((item, index) => {
    if (index === currentTrackIndex) {
      item.classList.add('active')
    } else {
      item.classList.remove('active')
    }
  })
}

// Progress bar click handler
function setupProgressBarClick() {
  const progressTrack = document.getElementById('progressTrack')
  if (progressTrack) {
    progressTrack.addEventListener('click', (e) => {
      if (!audioPlayer || !duration) return

      const rect = progressTrack.getBoundingClientRect()
      const clickX = e.clientX - rect.left
      const percentage = clickX / rect.width
      const seekTime = duration * percentage

      audioPlayer.currentTime = seekTime
    })
  }
}

// Initialize when page loads
document.addEventListener("DOMContentLoaded", () => {
  console.log('DOM loaded, initializing...')

  // Wait for config to load
  if (window.Config) {
    console.log('Config found, initializing page...')
    initializePage()
    initializeLoading()
    setupProgressBarClick()

    // Initialize audio player if music is enabled
    if (Config.musicPlayer.enabled && Config.musicPlayer.tracks.length > 0) {
      console.log('Initializing audio player...')
      initializeAudioPlayer()
    }
  } else {
    console.log('Config not found, retrying...')
    // Retry after a short delay if config not loaded
    setTimeout(() => {
      if (window.Config) {
        console.log('Config found on retry, initializing...')
        initializePage()
        initializeLoading()
        setupProgressBarClick()
      } else {
        console.error('Config still not found after retry')
      }
    }, 500)
  }
})

// Handle escape key to close panel
document.addEventListener("keydown", (event) => {
  if (event.key === "Escape" && currentSection && Config.general.enableEscapeKey) {
    closePanel()
  }
})

// FiveM NUI Message Handler
window.addEventListener('message', (event) => {
  const data = event.data

  switch(data.type) {
    case 'loadProgress':
      // Update progress from client.lua
      progress = data.progress

      // Update old progress bar (if exists)
      const progressFill = document.getElementById("progressFill")
      const progressText = document.getElementById("progressText")
      if (progressFill) progressFill.style.width = progress + "%"
      if (progressText) progressText.textContent = Math.round(progress) + "%"

      // Update loading circle
      updateLoadingCircle(progress)

      // Update loading percentage
      const loadingPercentage = document.getElementById("loadingPercentage")
      if (loadingPercentage) loadingPercentage.textContent = Math.round(progress) + "%"

      // Update loading step if provided
      if (data.step) {
        const loadingStep = document.getElementById("loadingStep")
        if (loadingStep) loadingStep.textContent = data.step
      }
      break

    case 'loadingComplete':
      // Loading completed, prepare for fade out
      setTimeout(() => {
        document.body.style.opacity = '0'
        document.body.style.transition = 'opacity 1s ease-out'
      }, 500)
      break

    case 'updateConfig':
      // Allow runtime config updates
      if (data.config) {
        window.Config = { ...window.Config, ...data.config }
        initializePage()
      }
      break
  }
})

// Check if running in FiveM
const isFiveM = window.invokeNative !== undefined

// Disable context menu and F12 in FiveM
if (isFiveM) {
  document.addEventListener('contextmenu', (e) => e.preventDefault())
  document.addEventListener('keydown', (e) => {
    if (e.key === 'F12' || (e.ctrlKey && e.shiftKey && e.key === 'I')) {
      e.preventDefault()
    }
  })
}
