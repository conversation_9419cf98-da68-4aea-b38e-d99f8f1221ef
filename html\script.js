// Global variables
let currentSection = null
let progress = 0
let isPlaying = false
let isMuted = false
const currentStepIndex = 0

// Loading steps
const loadingSteps = [
  "Sunucuya bağlanıyor...",
  "Kaynaklar indiriliyor...",
  "<PERSON><PERSON> yükleniyor...",
  "Karakter verileri alınıyor...",
  "Son hazırlıklar yapılıyor...",
  "Oyuna giriş yapılıyor...",
]

// Data
const announcements = [
  {
    type: "update",
    title: "Yeni Güncelleme v2.1.0",
    content: "Yeni araçlar ve iş sistemi eklendi!",
    date: "15 Aralık 2024",
    icon: "fas fa-exclamation-circle",
    color: "blue",
  },
  {
    type: "event",
    title: "Haftalık Etkinlik",
    content: "Bu akşam saat 21:00'da drag yarışı!",
    date: "17 Aralık 2024",
    icon: "fas fa-calendar",
    color: "green",
  },
  {
    type: "info",
    title: "<PERSON><PERSON><PERSON>",
    content: "Yarın 14:00-16:00 arası bakım yapılacak.",
    date: "18 Aralık 2024",
    icon: "fas fa-bell",
    color: "yellow",
  },
]

const serverRules = [
  "Roleplay kurallarına uyun ve karakterinizi koruyun",
  "Diğer oyunculara saygılı olun ve toxic davranış sergilemeyin",
  "Meta-gaming (OOC bilgi kullanımı) kesinlikle yasaktır",
  "RDM (Random Death Match) ve VDM (Vehicle Death Match) yapmayın",
  "Mikrofon kullanımı zorunludur, kaliteli ses gereklidir",
  "Fail RP yapmayın, gerçekçi davranın",
  "Power gaming yasaktır, limitlerinizi bilin",
]

const socialLinks = [
  {
    name: "Discord",
    icon: "fab fa-discord",
    url: "https://discord.gg/cityrp",
    class: "discord",
  },
  {
    name: "Website",
    icon: "fas fa-globe",
    url: "https://cityrp.com",
    class: "website",
  },
  {
    name: "YouTube",
    icon: "fab fa-youtube",
    url: "https://youtube.com/@cityrp",
    class: "youtube",
  },
]

// Initialize loading progress
function initializeLoading() {
  const interval = setInterval(() => {
    progress += Math.random() * 2

    if (progress >= 100) {
      progress = 100
      clearInterval(interval)
    }

    // Update progress bar
    document.getElementById("progressFill").style.width = progress + "%"
    document.getElementById("progressText").textContent = Math.round(progress) + "%"

    // Update loading step
    const stepIndex = Math.floor((progress / 100) * loadingSteps.length)
    const currentStep = loadingSteps[Math.min(stepIndex, loadingSteps.length - 1)]
    document.getElementById("loadingStep").textContent = currentStep
  }, 300)
}

// Toggle section
function toggleSection(section) {
  const panel = document.getElementById("sidePanel")
  const chevron = document.getElementById(section + "-chevron")

  if (currentSection === section) {
    // Close current section
    closePanel()
    return
  }

  // Reset all chevrons
  document.querySelectorAll(".menu-chevron").forEach((ch) => {
    ch.classList.remove("rotated")
    ch.classList.remove("fa-chevron-left")
    ch.classList.add("fa-chevron-right")
  })

  // Update current chevron
  chevron.classList.add("rotated")
  chevron.classList.remove("fa-chevron-right")
  chevron.classList.add("fa-chevron-left")

  currentSection = section
  loadPanelContent(section)
  panel.classList.add("open")
}

// Close panel
function closePanel() {
  const panel = document.getElementById("sidePanel")
  panel.classList.remove("open")
  currentSection = null

  // Reset all chevrons
  document.querySelectorAll(".menu-chevron").forEach((chevron) => {
    chevron.classList.remove("rotated")
    chevron.classList.remove("fa-chevron-left")
    chevron.classList.add("fa-chevron-right")
  })
}

// Load panel content
function loadPanelContent(section) {
  const titleElement = document.getElementById("panelTitle")
  const contentElement = document.getElementById("panelContent")

  let title = ""
  let content = ""

  switch (section) {
    case "announcements":
      title = "Duyurular"
      content = generateAnnouncementsContent()
      break
    case "rules":
      title = "Sunucu Kuralları"
      content = generateRulesContent()
      break
    case "social":
      title = "Topluluk"
      content = generateSocialContent()
      break
  }

  titleElement.textContent = title
  contentElement.innerHTML = content
}

// Generate announcements content
function generateAnnouncementsContent() {
  return announcements
    .map(
      (announcement) => `
        <div class="announcement-item">
            <div class="announcement-header">
                <i class="${announcement.icon} announcement-icon ${announcement.color}"></i>
                <div class="announcement-content">
                    <h4 class="announcement-title">${announcement.title}</h4>
                    <p class="announcement-text">${announcement.content}</p>
                    <p class="announcement-date">${announcement.date}</p>
                </div>
            </div>
        </div>
    `,
    )
    .join("")
}

// Generate rules content
function generateRulesContent() {
  return serverRules
    .map(
      (rule, index) => `
        <div class="rule-item">
            <div class="rule-number">
                <span>${index + 1}</span>
            </div>
            <p class="rule-text">${rule}</p>
        </div>
    `,
    )
    .join("")
}

// Generate social content
function generateSocialContent() {
  return socialLinks
    .map(
      (link) => `
        <a href="${link.url}" target="_blank" class="social-btn ${link.class}">
            <i class="${link.icon}"></i>
            <span>${link.name}</span>
        </a>
    `,
    )
    .join("")
}

// Music player functions
function togglePlay() {
  isPlaying = !isPlaying
  const playIcon = document.getElementById("playIcon")

  if (isPlaying) {
    playIcon.classList.remove("fa-play")
    playIcon.classList.add("fa-pause")
  } else {
    playIcon.classList.remove("fa-pause")
    playIcon.classList.add("fa-play")
  }
}

function toggleMute() {
  isMuted = !isMuted
  const muteIcon = document.getElementById("muteIcon")

  if (isMuted) {
    muteIcon.classList.remove("fa-volume-up")
    muteIcon.classList.add("fa-volume-mute")
  } else {
    muteIcon.classList.remove("fa-volume-mute")
    muteIcon.classList.add("fa-volume-up")
  }
}

function previousTrack() {
  console.log("Previous track")
}

function nextTrack() {
  console.log("Next track")
}

// Initialize when page loads
document.addEventListener("DOMContentLoaded", () => {
  initializeLoading()
})

// Handle escape key to close panel
document.addEventListener("keydown", (event) => {
  if (event.key === "Escape" && currentSection) {
    closePanel()
  }
})
