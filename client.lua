-- CityRP Loading Screen Client Script
local isLoadingFinished = false
local loadingProgress = 0
local currentLoadingStep = 1

-- Loading steps that match config.js
local loadingSteps = {
    "Sunucuya bağlanıyor...",
    "Kaynaklar indiriliyor...",
    "<PERSON><PERSON> yükleniyor...",
    "Karakter verileri alınıyor...",
    "Araçlar yükleniyor...",
    "İş sistemleri hazırlanıyor...",
    "Son hazırlıklar yapılıyor...",
    "<PERSON>yuna giriş yapılıyor..."
}

-- Send loading progress to NUI
local function UpdateLoadingProgress(progress, step)
    loadingProgress = progress
    currentLoadingStep = step or currentLoadingStep
    
    SendNUIMessage({
        type = 'loadProgress',
        progress = progress,
        step = loadingSteps[currentLoadingStep] or "Yükleniyor...",
        stepIndex = currentLoadingStep
    })
end

-- Simulate realistic loading progress
local function StartLoadingSimulation()
    Citizen.CreateThread(function()
        local progress = 0
        local step = 1
        
        -- Initial connection phase
        UpdateLoadingProgress(5, 1)
        Citizen.Wait(1000)
        
        -- Resource downloading phase
        for i = 5, 25, 2 do
            progress = i + math.random(0, 3)
            UpdateLoadingProgress(progress, 2)
            Citizen.Wait(200)
        end
        
        -- Map loading phase
        for i = 25, 50, 3 do
            progress = i + math.random(0, 2)
            UpdateLoadingProgress(progress, 3)
            Citizen.Wait(150)
        end
        
        -- Character data phase
        for i = 50, 70, 2 do
            progress = i + math.random(0, 3)
            UpdateLoadingProgress(progress, 4)
            Citizen.Wait(100)
        end
        
        -- Vehicle loading phase
        for i = 70, 85, 1 do
            progress = i + math.random(0, 2)
            UpdateLoadingProgress(progress, 5)
            Citizen.Wait(80)
        end
        
        -- Job systems phase
        for i = 85, 95, 1 do
            progress = i + math.random(0, 1)
            UpdateLoadingProgress(progress, 6)
            Citizen.Wait(100)
        end
        
        -- Final preparations
        UpdateLoadingProgress(98, 7)
        Citizen.Wait(500)
        
        -- Game ready
        UpdateLoadingProgress(100, 8)
        Citizen.Wait(1000)
        
        -- Finish loading
        FinishLoading()
    end)
end

-- Finish loading and hide screen
function FinishLoading()
    if not isLoadingFinished then
        isLoadingFinished = true
        
        -- Send completion message to NUI
        SendNUIMessage({
            type = 'loadingComplete'
        })
        
        -- Wait a bit for smooth transition
        Citizen.Wait(1500)
        
        -- Hide loading screen
        ShutdownLoadingScreen()
        ShutdownLoadingScreenNui()
        
        -- Trigger any post-loading events
        TriggerEvent('lotus_loading:finished')
        TriggerServerEvent('lotus_loading:playerLoaded')
        
        print("^2[CityRP Loading]^7 Loading screen finished successfully!")
    end
end

-- Handle game events
AddEventHandler('onClientResourceStart', function(resourceName)
    if GetCurrentResourceName() == resourceName then
        print("^2[CityRP Loading]^7 Loading screen started!")
        
        -- Start loading simulation
        StartLoadingSimulation()
    end
end)

-- Handle player spawning
AddEventHandler('playerSpawned', function()
    if not isLoadingFinished then
        FinishLoading()
    end
end)

-- Manual loading finish (can be triggered by other scripts)
RegisterNetEvent('lotus_loading:forceFinish')
AddEventHandler('lotus_loading:forceFinish', function()
    FinishLoading()
end)

-- Update loading progress manually (can be used by other scripts)
RegisterNetEvent('lotus_loading:updateProgress')
AddEventHandler('lotus_loading:updateProgress', function(progress, stepText)
    if not isLoadingFinished then
        UpdateLoadingProgress(progress, nil)
        
        if stepText then
            SendNUIMessage({
                type = 'loadProgress',
                progress = progress,
                step = stepText,
                stepIndex = currentLoadingStep
            })
        end
    end
end)

-- Export functions for other scripts
exports('updateProgress', function(progress, stepText)
    TriggerEvent('lotus_loading:updateProgress', progress, stepText)
end)

exports('finishLoading', function()
    TriggerEvent('lotus_loading:forceFinish')
end)

exports('isLoadingFinished', function()
    return isLoadingFinished
end)

-- Debug commands (only in development)
if GetConvar('lotus_loading_debug', 'false') == 'true' then
    RegisterCommand('loading_finish', function()
        FinishLoading()
    end, false)
    
    RegisterCommand('loading_progress', function(source, args)
        local progress = tonumber(args[1]) or 50
        UpdateLoadingProgress(progress)
    end, false)
end
