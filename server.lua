-- CityRP Loading Screen Server Script

-- Player loaded event
RegisterNetEvent('lotus_loading:playerLoaded')
AddEventHandler('lotus_loading:playerLoaded', function()
    local source = source
    local playerName = GetPlayerName(source)
    
    print(("^2[CityRP Loading]^7 Player %s (ID: %d) finished loading"):format(playerName, source))
    
    -- Trigger other scripts that player is ready
    TriggerEvent('lotus_loading:playerReady', source)
    TriggerClientEvent('lotus_loading:playerReady', -1, source)
end)

-- Force finish loading for a player (admin command)
RegisterCommand('forceloading', function(source, args, rawCommand)
    if source == 0 then -- Console command
        local targetId = tonumber(args[1])
        if targetId then
            TriggerClientEvent('lotus_loading:forceFinish', targetId)
            print(("^2[CityRP Loading]^7 Forced loading finish for player ID: %d"):format(targetId))
        else
            print("^1[CityRP Loading]^7 Usage: forceloading <player_id>")
        end
    else
        -- Check if player has admin permissions (customize this based on your admin system)
        if IsPlayerAceAllowed(source, 'command.forceloading') then
            local targetId = tonumber(args[1])
            if targetId then
                TriggerClientEvent('lotus_loading:forceFinish', targetId)
                TriggerClientEvent('chat:addMessage', source, {
                    color = {0, 255, 0},
                    multiline = true,
                    args = {"[Loading]", ("Forced loading finish for player ID: %d"):format(targetId)}
                })
            else
                TriggerClientEvent('chat:addMessage', source, {
                    color = {255, 0, 0},
                    multiline = true,
                    args = {"[Loading]", "Usage: /forceloading <player_id>"}
                })
            end
        else
            TriggerClientEvent('chat:addMessage', source, {
                color = {255, 0, 0},
                multiline = true,
                args = {"[Loading]", "You don't have permission to use this command"}
            })
        end
    end
end, false)

-- Update loading progress for a player (can be used by other scripts)
RegisterNetEvent('lotus_loading:updatePlayerProgress')
AddEventHandler('lotus_loading:updatePlayerProgress', function(targetId, progress, stepText)
    local source = source
    
    -- Check permissions or validate source
    if source == 0 or IsPlayerAceAllowed(source, 'command.updateloading') then
        TriggerClientEvent('lotus_loading:updateProgress', targetId, progress, stepText)
    end
end)

-- Export functions for other server scripts
exports('updatePlayerProgress', function(playerId, progress, stepText)
    TriggerClientEvent('lotus_loading:updateProgress', playerId, progress, stepText)
end)

exports('finishPlayerLoading', function(playerId)
    TriggerClientEvent('lotus_loading:forceFinish', playerId)
end)

-- Startup message
AddEventHandler('onResourceStart', function(resourceName)
    if GetCurrentResourceName() == resourceName then
        print("^2[CityRP Loading Screen]^7 Successfully started!")
        print("^3[CityRP Loading Screen]^7 Version: 2.1.0")
        print("^3[CityRP Loading Screen]^7 Author: CityRP Development")
    end
end)

-- Shutdown message
AddEventHandler('onResourceStop', function(resourceName)
    if GetCurrentResourceName() == resourceName then
        print("^1[CityRP Loading Screen]^7 Stopped!")
    end
end)
