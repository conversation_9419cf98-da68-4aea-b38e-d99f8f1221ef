// CityRP Loading Screen Configuration
const Config = {
    // Logo ve Başlık Ayarları
    logo: {
        title: "LOTUS",
        titleAccent: "LOADING",
        subtitle: "Lotus Development",
        // Logo resmi (images klasöründen çekilir)
        imageName: "lotuslogorw.png", // images/logo.png dosyası
        showImage: true, // Logo resmini göster/gizle
        imageSize: "120px" // Logo boyutu
    },

    // Arkaplan Video Ayarları
    backgroundVideo: {
        enabled: true,
        youtubeId: "FND0z4NF8Pg", // YouTube video ID'si
        autoplay: true,
        muted: true,
        loop: true
    },

    // Duyurular
    announcements: [
        {
            type: "update",
            title: "Lotus Loading v2.1.0",
            content: "Modern tasarım ve YouTube müzik çalar eklendi!",
            date: "15 Aralık 2024",
            icon: "fas fa-exclamation-circle",
            color: "blue"
        },
        {
            type: "event",
            title: "<PERSON><PERSON>",
            content: "Gelişmiş animasyonlar ve sarı-turuncu tema!",
            date: "17 Aralık 2024",
            icon: "fas fa-calendar",
            color: "green"
        },
        {
            type: "info",
            title: "Lotus Development",
            content: "Profesyonel FiveM script geliştirme hizmetleri.",
            date: "18 Aralık 2024",
            icon: "fas fa-bell",
            color: "yellow"
        }
    ],

    // Sunucu Kuralları
    serverRules: [
        "Roleplay kurallarına uyun ve karakterinizi koruyun",
        "Diğer oyunculara saygılı olun ve toxic davranış sergilemeyin",
        "Meta-gaming (OOC bilgi kullanımı) kesinlikle yasaktır",
        "RDM (Random Death Match) ve VDM (Vehicle Death Match) yapmayın",
        "Mikrofon kullanımı zorunludur, kaliteli ses gereklidir",
        "Fail RP yapmayın, gerçekçi davranın",
        "Power gaming yasaktır, limitlerinizi bilin",
        "Sunucu içi ekonomiye zarar verecek bug kullanımı yasaktır",
        "Karakterinizi öldürme (CK) sadece admin onayı ile yapılabilir",
        "Araç modifikasyonları roleplay mantığına uygun olmalıdır"
    ],

    // Topluluk Linkleri
    socialLinks: [
        {
            name: "Discord",
            icon: "fab fa-discord",
            url: "https://discord.gg/lotus",
            class: "discord"
        },
        {
            name: "Website",
            icon: "fas fa-globe",
            url: "https://lotus-development.com",
            class: "website"
        },
        {
            name: "YouTube",
            icon: "fab fa-youtube",
            url: "https://youtube.com/@lotus-dev",
            class: "youtube"
        },
        {
            name: "Instagram",
            icon: "fab fa-instagram",
            url: "https://instagram.com/lotus.dev",
            class: "instagram"
        },
        {
            name: "TikTok",
            icon: "fab fa-tiktok",
            url: "https://tiktok.com/@lotus.dev",
            class: "tiktok"
        }
    ],

    // Müzik Çalar Ayarları
    musicPlayer: {
        enabled: true,
        tracks: [
            {
                name: "Lo-Fi Hip Hop Mix",
                artist: "ChilledCow",
                duration: "3:45",
                url: "music/lofi-mix.mp3"
            },
            {
                name: "Synthwave Mix",
                artist: "NewRetroWave",
                duration: "4:20",
                url: "music/synthwave-mix.mp3"
            },
            {
                name: "Chill Gaming Music",
                artist: "GameChops",
                duration: "5:15",
                url: "music/chill-gaming.mp3"
            },
            {
                name: "Cyberpunk 2077 Mix",
                artist: "Cyber Music",
                duration: "6:30",
                url: "music/cyberpunk-mix.mp3"
            },
            {
                name: "GTA Vice City Vibes",
                artist: "80s Retrowave",
                duration: "4:45",
                url: "music/vice-city.mp3"
            }
        ],
        autoplay: true,
        volume: 0.3
    },

    // Yükleme Adımları
    loadingSteps: [
        "Sunucuya bağlanıyor...",
        "Kaynaklar indiriliyor...",
        "Harita yükleniyor...",
        "Karakter verileri alınıyor...",
        "Araçlar yükleniyor...",
        "İş sistemleri hazırlanıyor...",
        "Son hazırlıklar yapılıyor...",
        "Oyuna giriş yapılıyor..."
    ],

    // Project Team
    projectTeam: {
        enabled: true,
        title: "Project Team",
        members: [
            {
                name: "John Doe",
                role: "Lead Developer",
                avatar: "images/team/john.jpg", // veya Discord avatar URL'si
                discordId: "123456789012345678" // Discord ID (opsiyonel)
            },
            {
                name: "Jane Smith",
                role: "UI/UX Designer",
                avatar: "images/team/jane.jpg",
                discordId: "987654321098765432"
            },
            {
                name: "Mike Johnson",
                role: "Backend Developer",
                avatar: "images/team/mike.jpg",
                discordId: "456789123456789123"
            },
            {
                name: "Sarah Wilson",
                role: "Project Manager",
                avatar: "images/team/sarah.jpg",
                discordId: "789123456789123456"
            }
        ]
    },

    // Genel Ayarlar
    general: {
        version: "v2.1.0",
        copyright: "© 2024 Lotus Development. Tüm hakları saklıdır.",
        loadingSpeed: 300, // ms cinsinden yükleme hızı
        enableEscapeKey: true, // ESC tuşu ile panel kapatma
        enableSoundEffects: true // Ses efektleri
    }
};

// Config'i global olarak kullanılabilir yap
window.Config = Config;
