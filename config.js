// CityRP Loading Screen Configuration
const Config = {
    // Logo ve Başlık Ayarları
    logo: {
        title: "CITY",
        titleAccent: "RP",
        subtitle: "Gerçekçi Roleplay Deneyimi"
    },

    // Arkaplan Video Ayarları
    backgroundVideo: {
        enabled: true,
        youtubeId: "jfKfPfyJRdk", // YouTube video ID'si
        autoplay: true,
        muted: true,
        loop: true
    },

    // Duyurular
    announcements: [
        {
            type: "update",
            title: "Yeni Güncelleme v2.1.0",
            content: "Yeni araçlar ve iş sistemi eklendi!",
            date: "15 Aralık 2024",
            icon: "fas fa-exclamation-circle",
            color: "blue"
        },
        {
            type: "event",
            title: "Haftalık Etkinlik",
            content: "Bu akşam saat 21:00'da drag yarışı!",
            date: "17 Aralık 2024",
            icon: "fas fa-calendar",
            color: "green"
        },
        {
            type: "info",
            title: "<PERSON><PERSON><PERSON> Bakımı",
            content: "Yarın 14:00-16:00 arası bakım yapılacak.",
            date: "18 Aralık 2024",
            icon: "fas fa-bell",
            color: "yellow"
        }
    ],

    // Sunucu Kuralları
    serverRules: [
        "Roleplay kurallarına uyun ve karakterinizi koruyun",
        "Diğer oyunculara saygılı olun ve toxic davranış sergilemeyin",
        "Meta-gaming (OOC bilgi kullanımı) kesinlikle yasaktır",
        "RDM (Random Death Match) ve VDM (Vehicle Death Match) yapmayın",
        "Mikrofon kullanımı zorunludur, kaliteli ses gereklidir",
        "Fail RP yapmayın, gerçekçi davranın",
        "Power gaming yasaktır, limitlerinizi bilin",
        "Sunucu içi ekonomiye zarar verecek bug kullanımı yasaktır",
        "Karakterinizi öldürme (CK) sadece admin onayı ile yapılabilir",
        "Araç modifikasyonları roleplay mantığına uygun olmalıdır"
    ],

    // Topluluk Linkleri
    socialLinks: [
        {
            name: "Discord",
            icon: "fab fa-discord",
            url: "https://discord.gg/cityrp",
            class: "discord"
        },
        {
            name: "Website",
            icon: "fas fa-globe",
            url: "https://cityrp.com",
            class: "website"
        },
        {
            name: "YouTube",
            icon: "fab fa-youtube",
            url: "https://youtube.com/@cityrp",
            class: "youtube"
        },
        {
            name: "Instagram",
            icon: "fab fa-instagram",
            url: "https://instagram.com/cityrp",
            class: "instagram"
        },
        {
            name: "TikTok",
            icon: "fab fa-tiktok",
            url: "https://tiktok.com/@cityrp",
            class: "tiktok"
        }
    ],

    // Müzik Çalar Ayarları
    musicPlayer: {
        enabled: true,
        tracks: [
            {
                name: "Lo-Fi Hip Hop Mix",
                artist: "Arka Plan Müziği",
                duration: "4:12",
                url: "music/lofi-mix.mp3"
            },
            {
                name: "Chill Vibes",
                artist: "Relaxing Beats",
                duration: "3:45",
                url: "music/chill-vibes.mp3"
            },
            {
                name: "City Nights",
                artist: "Urban Sounds",
                duration: "5:20",
                url: "music/city-nights.mp3"
            },
            {
                name: "Midnight Drive",
                artist: "Synthwave",
                duration: "4:33",
                url: "music/midnight-drive.mp3"
            }
        ],
        autoplay: false,
        volume: 0.5
    },

    // Yükleme Adımları
    loadingSteps: [
        "Sunucuya bağlanıyor...",
        "Kaynaklar indiriliyor...",
        "Harita yükleniyor...",
        "Karakter verileri alınıyor...",
        "Araçlar yükleniyor...",
        "İş sistemleri hazırlanıyor...",
        "Son hazırlıklar yapılıyor...",
        "Oyuna giriş yapılıyor..."
    ],

    // Genel Ayarlar
    general: {
        version: "v2.1.0",
        copyright: "© 2024 CityRP. Tüm hakları saklıdır.",
        loadingSpeed: 300, // ms cinsinden yükleme hızı
        enableEscapeKey: true, // ESC tuşu ile panel kapatma
        enableSoundEffects: true // Ses efektleri
    }
};

// Config'i global olarak kullanılabilir yap
window.Config = Config;
