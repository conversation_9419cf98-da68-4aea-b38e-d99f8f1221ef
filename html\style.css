:root {
  /* Modern Orange-Yellow Color Palette */
  --primary-orange: #ff6b35;
  --primary-yellow: #ffa726;
  --accent-gold: #ffb74d;
  --warm-orange: #ff8a50;
  --deep-orange: #e65100;
  --light-amber: #fff3c4;
  --dark-bg: #1a0f0a;
  --darker-bg: #0f0704;
  --card-bg: rgba(26, 15, 10, 0.95);
  --glass-bg: rgba(255, 167, 38, 0.1);
  --border-glow: rgba(255, 107, 53, 0.3);
  --text-primary: #ffffff;
  --text-secondary: #ffcc80;
  --text-muted: #bf9000;
  --shadow-warm: rgba(255, 107, 53, 0.4);
  --gradient-primary: linear-gradient(135deg, var(--primary-orange) 0%, var(--primary-yellow) 100%);
  --gradient-secondary: linear-gradient(135deg, var(--warm-orange) 0%, var(--accent-gold) 100%);
  --gradient-bg: linear-gradient(135deg, var(--dark-bg) 0%, var(--darker-bg) 100%);
  --gradient-card: linear-gradient(135deg, rgba(255, 107, 53, 0.15) 0%, rgba(255, 167, 38, 0.1) 100%);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: var(--gradient-bg);
  color: var(--text-primary);
  overflow: hidden;
  height: 100vh;
  position: relative;
}

body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(255, 107, 53, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 167, 38, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(255, 183, 77, 0.02) 0%, transparent 50%);
  pointer-events: none;
  z-index: 1;
}

/* Video Background */
.video-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
}

.video-background iframe {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.video-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
}

/* Main Content */
.main-content {
  position: relative;
  z-index: 10;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Center Logo */
.center-logo {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  pointer-events: none;
}

.logo-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2rem;
}

/* Loading Circle */
.loading-circle-container {
  position: relative;
  width: 150px;
  height: 150px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-circle {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  transform: rotate(-90deg);
  z-index: 1;
}

.loading-circle-bg {
  fill: none;
  stroke: rgba(255, 107, 53, 0.2);
  stroke-width: 4;
}

.loading-circle-progress {
  fill: none;
  stroke: url(#circleGradient);
  stroke-width: 6;
  stroke-linecap: round;
  stroke-dasharray: 283;
  stroke-dashoffset: 283;
  transition: stroke-dashoffset 0.5s ease;
  filter: drop-shadow(0 0 12px var(--shadow-warm));
}

.loading-circle-content {
  position: relative;
  z-index: 2;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.loading-title {
  color: var(--text-primary);
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.5);
}

.loading-percentage {
  color: var(--primary-orange);
  font-size: 1.5rem;
  font-weight: 700;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.5);
}

/* Logo Text */
.logo-text-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.logo-main-text {
  color: white;
  font-size: 3rem;
  font-weight: 900;
  letter-spacing: -0.02em;
  text-shadow: 0 4px 12px rgba(0, 0, 0, 0.5);
  margin: 0;
}

.logo-sub-text {
  color: white;
  font-size: 1.25rem;
  font-weight: 500;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.5);
  opacity: 0.9;
  margin: 0;
}

/* Menu Buttons */
.menu-buttons {
  position: absolute;
  top: 2rem;
  right: 2rem;
  width: 22rem;
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
  pointer-events: auto;
  z-index: 40;
}

.menu-card {
  background: var(--gradient-card);
  border: 1px solid var(--border-glow);
  border-radius: 1.25rem;
  backdrop-filter: blur(20px);
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
  overflow: hidden;
  position: relative;
}

.menu-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s ease;
}

.menu-card:hover::before {
  left: 100%;
}

.menu-card:hover {
  transform: translateY(-2px);
  border-color: var(--primary-orange);
  box-shadow:
    0 25px 50px rgba(0, 0, 0, 0.4),
    0 0 30px var(--shadow-warm),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.menu-btn {
  width: 100%;
  padding: 1.25rem;
  background: transparent;
  border: none;
  color: var(--text-primary);
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all 0.3s ease;
  position: relative;
  z-index: 2;
}

.menu-btn:hover {
  color: var(--light-amber);
}

.menu-btn-content {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.menu-btn-content i {
  font-size: 1.25rem;
  color: var(--primary-yellow);
}

.menu-chevron {
  width: 1.25rem;
  height: 1.25rem;
  color: var(--accent-gold);
  transition: all 0.3s ease;
}

.menu-chevron.rotated {
  transform: rotate(180deg);
  color: var(--primary-orange);
}

/* Side Panel */
.side-panel {
  position: fixed;
  top: 2rem;
  right: 0rem;
  width: 26rem;
  background: var(--gradient-card);
  border: 1px solid var(--border-glow);
  border-radius: 1.5rem;
  backdrop-filter: blur(25px);
  box-shadow:
    0 30px 60px rgba(0, 0, 0, 0.4),
    0 0 40px var(--shadow-warm),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  transform: translateX(100%);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 50;
  max-height: 28rem;
  overflow: hidden;
}

.side-panel.open {
  transform: translateX(-26rem);
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-glow);
  background: linear-gradient(135deg, rgba(255, 107, 53, 0.1) 0%, rgba(255, 167, 38, 0.05) 100%);
}

.panel-title {
  color: var(--text-primary);
  font-size: 1.25rem;
  font-weight: 700;
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.panel-close-btn {
  background: rgba(255, 107, 53, 0.1);
  border: 1px solid var(--border-glow);
  color: var(--primary-yellow);
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.panel-close-btn:hover {
  background: var(--primary-orange);
  color: white;
  transform: scale(1.1);
  box-shadow: 0 8px 20px var(--shadow-warm);
}

.panel-content {
  padding: 1.5rem;
  max-height: 24rem;
  overflow-y: auto;
}

.panel-content::-webkit-scrollbar {
  width: 6px;
}

.panel-content::-webkit-scrollbar-track {
  background: rgba(255, 107, 53, 0.1);
  border-radius: 3px;
}

.panel-content::-webkit-scrollbar-thumb {
  background: var(--gradient-primary);
  border-radius: 3px;
}

.panel-content::-webkit-scrollbar-thumb:hover {
  background: var(--gradient-secondary);
}

/* Panel Content Styles */
.announcement-item {
  background: linear-gradient(135deg, rgba(255, 107, 53, 0.1) 0%, rgba(255, 167, 38, 0.05) 100%);
  border-radius: 1rem;
  padding: 1.25rem;
  border: 1px solid var(--border-glow);
  margin-bottom: 1rem;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.announcement-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, transparent 0%, rgba(255, 255, 255, 0.05) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.announcement-item:hover::before {
  opacity: 1;
}

.announcement-item:hover {
  transform: translateY(-2px);
  border-color: var(--primary-orange);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
}

.announcement-header {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
}

.announcement-icon {
  width: 1.5rem;
  height: 1.5rem;
  margin-top: 0.125rem;
  flex-shrink: 0;
}

.announcement-icon.blue {
  color: var(--primary-yellow);
}
.announcement-icon.green {
  color: var(--accent-gold);
}
.announcement-icon.yellow {
  color: var(--warm-orange);
}

.announcement-content {
  flex: 1;
  min-width: 0;
}

.announcement-title {
  color: var(--text-primary);
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.announcement-text {
  color: var(--text-secondary);
  font-size: 0.875rem;
  line-height: 1.6;
  margin-bottom: 0.75rem;
}

.announcement-date {
  color: var(--text-muted);
  font-size: 0.8rem;
  font-weight: 500;
}

.rule-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem;
  background: linear-gradient(135deg, rgba(255, 107, 53, 0.08) 0%, rgba(255, 167, 38, 0.04) 100%);
  border-radius: 0.75rem;
  margin-bottom: 1rem;
  border: 1px solid rgba(255, 107, 53, 0.2);
  transition: all 0.3s ease;
}

.rule-item:hover {
  transform: translateX(5px);
  border-color: var(--primary-orange);
  background: linear-gradient(135deg, rgba(255, 107, 53, 0.15) 0%, rgba(255, 167, 38, 0.08) 100%);
}

.rule-number {
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  background: var(--gradient-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  margin-top: 0.125rem;
  box-shadow: 0 4px 12px var(--shadow-warm);
}

.rule-number span {
  color: white;
  font-size: 0.875rem;
  font-weight: 700;
}

.rule-text {
  color: var(--text-secondary);
  font-size: 0.9rem;
  line-height: 1.6;
  font-weight: 400;
}

.social-btn {
  width: 100%;
  height: 3.5rem;
  border: 1px solid var(--border-glow);
  border-radius: 1rem;
  color: white;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 1rem;
  transition: all 0.3s ease;
  text-decoration: none;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
}

.social-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.social-btn:hover::before {
  left: 100%;
}

.social-btn:hover {
  transform: translateY(-3px) scale(1.02);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
}

.social-btn i {
  font-size: 1.25rem;
}

.social-btn.discord {
  background: linear-gradient(135deg, #5865f2 0%, var(--primary-orange) 100%);
}
.social-btn.discord:hover {
  background: linear-gradient(135deg, #4752c4 0%, var(--deep-orange) 100%);
}
.social-btn.website {
  background: linear-gradient(135deg, var(--primary-orange) 0%, var(--primary-yellow) 100%);
}
.social-btn.website:hover {
  background: linear-gradient(135deg, var(--deep-orange) 0%, var(--warm-orange) 100%);
}
.social-btn.youtube {
  background: linear-gradient(135deg, #dc2626 0%, var(--primary-orange) 100%);
}
.social-btn.youtube:hover {
  background: linear-gradient(135deg, #b91c1c 0%, var(--deep-orange) 100%);
}
.social-btn.instagram {
  background: linear-gradient(135deg, #f09433 0%, var(--primary-orange) 50%, var(--primary-yellow) 100%);
}
.social-btn.instagram:hover {
  background: linear-gradient(135deg, #e6683c 0%, var(--deep-orange) 50%, var(--warm-orange) 100%);
}
.social-btn.tiktok {
  background: linear-gradient(135deg, #000000 0%, var(--dark-bg) 50%, var(--primary-orange) 100%);
}
.social-btn.tiktok:hover {
  background: linear-gradient(135deg, #1a1a1a 0%, var(--darker-bg) 50%, var(--deep-orange) 100%);
}

/* Loading Container */
.loading-container {
  position: absolute;
  bottom: 10rem;
  left: 50%;
  transform: translateX(-50%);
  pointer-events: auto;
}

.loading-card {
  background: var(--gradient-card);
  border: 1px solid var(--border-glow);
  border-radius: 1.5rem;
  backdrop-filter: blur(25px);
  box-shadow:
    0 30px 60px rgba(0, 0, 0, 0.4),
    0 0 40px var(--shadow-warm),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  padding: 2rem;
  width: 28rem;
  position: relative;
  overflow: hidden;
}

.loading-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, transparent 0%, rgba(255, 255, 255, 0.05) 100%);
  pointer-events: none;
}

.loading-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  position: relative;
  z-index: 2;
}

.loading-title {
  color: var(--text-primary);
  font-size: 1.5rem;
  font-weight: 700;
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.loading-badge {
  background: var(--gradient-primary);
  color: white;
  border: 1px solid var(--border-glow);
  padding: 0.5rem 1rem;
  border-radius: 2rem;
  font-size: 1rem;
  font-weight: 600;
  box-shadow: 0 8px 20px var(--shadow-warm);
}

.progress-container {
  position: relative;
  margin-bottom: 1.5rem;
  z-index: 2;
}

.progress-bar {
  height: 1rem;
  background: rgba(255, 107, 53, 0.2);
  border-radius: 2rem;
  overflow: hidden;
  position: relative;
  border: 1px solid var(--border-glow);
}

.progress-fill {
  height: 100%;
  background: var(--gradient-primary);
  border-radius: 2rem;
  transition: width 0.5s ease-out;
  width: 0%;
  position: relative;
  box-shadow: 0 0 20px var(--shadow-warm);
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.4) 50%, transparent 100%);
  animation: shimmer 2s infinite;
}

.progress-pulse {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2rem;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.loading-step {
  color: var(--text-secondary);
  font-size: 1rem;
  text-align: center;
  font-weight: 500;
  position: relative;
  z-index: 2;
}

/* Music Player */
.music-player {
  position: absolute;
  bottom: 2rem;
  left: 50%;
  transform: translateX(-50%);
  pointer-events: auto;
  z-index: 100;
}

.music-card {
  background: var(--gradient-card);
  border: 1px solid var(--border-glow);
  border-radius: 1.5rem;
  backdrop-filter: blur(25px);
  box-shadow:
    0 30px 60px rgba(0, 0, 0, 0.4),
    0 0 40px var(--shadow-warm),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  padding: 1.5rem;
  display: flex;
  align-items: center;
  gap: 1.5rem;
  min-width: 450px;
  transition: all 0.4s ease;
  position: relative;
  overflow: hidden;
}

.music-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, transparent 0%, rgba(255, 255, 255, 0.05) 100%);
  pointer-events: none;
}

.music-card:hover {
  transform: translateY(-5px) scale(1.02);
  border-color: var(--primary-orange);
  box-shadow:
    0 40px 80px rgba(0, 0, 0, 0.5),
    0 0 60px var(--shadow-warm),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.album-art {
  width: 5rem;
  height: 5rem;
  background: var(--gradient-primary);
  border-radius: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  position: relative;
  overflow: hidden;
  box-shadow: 0 15px 35px var(--shadow-warm);
  transition: all 0.4s ease;
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.album-art:hover {
  transform: scale(1.1) rotate(5deg);
  box-shadow: 0 20px 50px var(--shadow-warm);
}

.album-art img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 0.75rem;
}

.album-art i {
  color: white;
  font-size: 2rem;
  text-shadow: 0 4px 12px rgba(0, 0, 0, 0.5);
}

.track-info {
  flex: 1;
  min-width: 0;
  position: relative;
  z-index: 2;
}

.track-name {
  color: var(--text-primary);
  font-size: 1.125rem;
  font-weight: 700;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 0.5rem;
  text-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
}

.track-subtitle {
  color: var(--text-secondary);
  font-size: 0.9rem;
  margin-bottom: 0.75rem;
  font-weight: 500;
}

.track-progress {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.time-current,
.time-total {
  color: var(--text-muted);
  font-size: 0.875rem;
  font-weight: 600;
  min-width: 3rem;
}

.progress-track {
  flex: 1;
  height: 0.5rem;
  background: rgba(255, 107, 53, 0.3);
  border-radius: 2rem;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 107, 53, 0.4);
}

.progress-track:hover {
  height: 0.625rem;
  background: rgba(255, 107, 53, 0.4);
  box-shadow: 0 0 15px var(--shadow-warm);
}

.progress-track-fill {
  width: 0%;
  height: 100%;
  background: var(--gradient-primary);
  border-radius: 2rem;
  transition: width 0.3s ease;
  position: relative;
  box-shadow: 0 0 10px var(--shadow-warm);
}

.progress-track-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.4) 50%, transparent 100%);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.music-controls {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  position: relative;
  z-index: 2;
}

.control-btn {
  background: rgba(255, 107, 53, 0.2);
  border: 1px solid var(--border-glow);
  color: var(--text-primary);
  width: 2.75rem;
  height: 2.75rem;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  backdrop-filter: blur(15px);
  position: relative;
  overflow: hidden;
}

.control-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--gradient-primary);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.control-btn:hover::before {
  opacity: 1;
}

.control-btn:hover {
  border-color: var(--primary-orange);
  transform: translateY(-2px) scale(1.1);
  box-shadow: 0 8px 20px var(--shadow-warm);
}

.control-btn:active {
  transform: translateY(0) scale(1.05);
}

.control-btn i {
  position: relative;
  z-index: 2;
}

.play-btn {
  width: 3.5rem;
  height: 3.5rem;
  background: var(--gradient-primary);
  border: 2px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 15px 35px var(--shadow-warm);
}

.play-btn:hover {
  background: var(--gradient-secondary);
  box-shadow: 0 20px 50px var(--shadow-warm);
  transform: translateY(-3px) scale(1.1);
}

/* Playlist */
.playlist {
  position: absolute;
  bottom: 100%;
  left: 0;
  right: 0;
  background: var(--gradient-card);
  border: 1px solid var(--border-glow);
  border-radius: 1.5rem;
  backdrop-filter: blur(25px);
  box-shadow:
    0 30px 60px rgba(0, 0, 0, 0.4),
    0 0 40px var(--shadow-warm),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  margin-bottom: 1.5rem;
  max-height: 350px;
  overflow: hidden;
  transform: translateY(30px) scale(0.9);
  opacity: 0;
  visibility: hidden;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.playlist.open {
  transform: translateY(0) scale(1);
  opacity: 1;
  visibility: visible;
}

.playlist-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-glow);
  background: linear-gradient(135deg, rgba(255, 107, 53, 0.1) 0%, rgba(255, 167, 38, 0.05) 100%);
}

.playlist-header h4 {
  color: var(--text-primary);
  font-size: 1.125rem;
  font-weight: 700;
  margin: 0;
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.playlist-close {
  background: rgba(255, 107, 53, 0.2);
  border: 1px solid var(--border-glow);
  color: var(--primary-yellow);
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 50%;
  transition: all 0.3s ease;
  width: 2.5rem;
  height: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.playlist-close:hover {
  color: white;
  background: var(--primary-orange);
  transform: scale(1.1);
  box-shadow: 0 8px 20px var(--shadow-warm);
}

.playlist-content {
  max-height: 280px;
  overflow-y: auto;
  padding: 1rem;
}

.playlist-content::-webkit-scrollbar {
  width: 6px;
}

.playlist-content::-webkit-scrollbar-track {
  background: rgba(255, 107, 53, 0.1);
  border-radius: 3px;
}

.playlist-content::-webkit-scrollbar-thumb {
  background: var(--gradient-primary);
  border-radius: 3px;
}

.playlist-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  border-radius: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 0.5rem;
  border: 1px solid transparent;
  position: relative;
  overflow: hidden;
}

.playlist-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 107, 53, 0.1) 0%, rgba(255, 167, 38, 0.05) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.playlist-item:hover::before {
  opacity: 1;
}

.playlist-item:hover {
  transform: translateX(5px);
  border-color: var(--border-glow);
}

.playlist-item.active {
  background: var(--gradient-card);
  border-color: var(--primary-orange);
  box-shadow: 0 8px 20px var(--shadow-warm);
}

.playlist-item.active::before {
  opacity: 1;
}

.playlist-item-thumbnail {
  width: 3.5rem;
  height: 3.5rem;
  border-radius: 0.75rem;
  overflow: hidden;
  flex-shrink: 0;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.playlist-item-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.playlist-item-info {
  flex: 1;
  min-width: 0;
  position: relative;
  z-index: 2;
}

.playlist-item-name {
  color: var(--text-primary);
  font-size: 0.9rem;
  font-weight: 600;
  margin-bottom: 0.25rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.playlist-item-artist {
  color: var(--text-secondary);
  font-size: 0.8rem;
  font-weight: 400;
}

.playlist-item-duration {
  color: var(--text-muted);
  font-size: 0.8rem;
  flex-shrink: 0;
  font-weight: 500;
  position: relative;
  z-index: 2;
}

/* Footer */
.footer {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  padding-bottom: 1rem;
}

.footer p {
  color: var(--text-muted);
  font-size: 0.875rem;
  font-weight: 500;
  text-shadow: 0 10px 25px rgba(0, 0, 0, 0.5);
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Responsive */
@media (max-width: 768px) {
  .loading-circle-container {
    width: 120px;
    height: 120px;
  }

  .loading-title {
    font-size: 0.875rem;
  }

  .loading-percentage {
    font-size: 1.25rem;
  }

  .logo-main-text {
    font-size: 2.5rem;
  }

  .logo-sub-text {
    font-size: 1rem;
  }

  .menu-buttons {
    width: 18rem;
    right: 1rem;
    gap: 1rem;
  }

  .side-panel {
    width: 22rem;
  }

  .side-panel.open {
    transform: translateX(-22rem);
  }

  .loading-card {
    width: 24rem;
    padding: 1.5rem;
  }

  .music-card {
    min-width: 350px;
    padding: 1.25rem;
    gap: 1.25rem;
  }

  .album-art {
    width: 4rem;
    height: 4rem;
  }

  .track-name {
    font-size: 1rem;
  }

  .track-subtitle {
    font-size: 0.85rem;
  }

  .control-btn {
    width: 2.5rem;
    height: 2.5rem;
  }

  .play-btn {
    width: 3rem;
    height: 3rem;
  }

  .playlist {
    max-height: 280px;
  }

  .playlist-item-thumbnail {
    width: 3rem;
    height: 3rem;
  }

  .playlist-item {
    padding: 0.75rem;
  }
}

@media (max-width: 480px) {
  .loading-circle-container {
    width: 100px;
    height: 100px;
  }

  .loading-title {
    font-size: 0.75rem;
  }

  .loading-percentage {
    font-size: 1rem;
  }

  .logo-main-text {
    font-size: 2rem;
  }

  .logo-sub-text {
    font-size: 0.875rem;
  }

  .menu-buttons {
    width: 16rem;
  }

  .side-panel {
    width: 20rem;
  }

  .side-panel.open {
    transform: translateX(-20rem);
  }

  .loading-card {
    width: 20rem;
    padding: 1.25rem;
  }

  .music-card {
    min-width: 300px;
    padding: 1rem;
    gap: 1rem;
  }

  .album-art {
    width: 3.5rem;
    height: 3.5rem;
  }
}
