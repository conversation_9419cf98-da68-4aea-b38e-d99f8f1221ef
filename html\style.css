* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, sans-serif;
  overflow: hidden;
  height: 100vh;
}

/* Video Background */
.video-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
}

.video-background iframe {
  width: 150%;
  height: 150%;
  transform: scale(1.5);
  object-fit: cover;
}

.video-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
}

/* Main Content */
.main-content {
  position: relative;
  z-index: 10;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Center Logo */
.center-logo {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  pointer-events: none;
}

.logo-text {
  font-size: 6rem;
  font-weight: bold;
  color: white;
  margin-bottom: 1rem;
  letter-spacing: -0.025em;
  text-shadow: 0 25px 50px rgba(0, 0, 0, 0.5);
}

.logo-accent {
  color: #60a5fa;
}

.logo-subtitle {
  color: #e2e8f0;
  font-size: 1.25rem;
  text-shadow: 0 10px 25px rgba(0, 0, 0, 0.5);
}

/* Menu Buttons */
.menu-buttons {
  position: absolute;
  top: 1.5rem;
  right: 1.5rem;
  width: 20rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  pointer-events: auto;
  z-index: 40;
}

.menu-card {
  background: rgba(15, 23, 42, 0.9);
  border: 1px solid #475569;
  border-radius: 0.5rem;
  backdrop-filter: blur(12px);
}

.menu-btn {
  width: 100%;
  padding: 1rem;
  background: transparent;
  border: none;
  color: white;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: background-color 0.2s;
}

.menu-btn:hover {
  background: rgba(30, 41, 59, 0.5);
}

.menu-btn-content {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.menu-chevron {
  width: 1.25rem;
  height: 1.25rem;
  transition: transform 0.3s;
}

.menu-chevron.rotated {
  transform: rotate(180deg);
}

/* Side Panel */
.side-panel {
  position: fixed;
  top: 1.5rem;
  right: 0rem;
  width: 24rem;
  background: rgba(15, 23, 42, 0.95);
  border: 1px solid #475569;
  border-radius: 0.5rem;
  backdrop-filter: blur(12px);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
  transform: translateX(100%);
  transition: transform 0.3s ease-in-out;
  z-index: 50;
  max-height: 24rem;
  overflow: hidden;
}

.side-panel.open {
  transform: translateX(-24rem);
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid #475569;
}

.panel-title {
  color: white;
  font-size: 1.125rem;
  font-weight: 600;
}

.panel-close-btn {
  background: transparent;
  border: none;
  color: white;
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
}

.panel-close-btn:hover {
  background: rgba(30, 41, 59, 0.5);
}

.panel-content {
  padding: 1rem;
  max-height: 24rem;
  overflow-y: auto;
}

/* Panel Content Styles */
.announcement-item {
  background: rgba(30, 41, 59, 0.5);
  border-radius: 0.5rem;
  padding: 1rem;
  border: 1px solid #475569;
  margin-bottom: 0.75rem;
}

.announcement-header {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
}

.announcement-icon {
  width: 1.25rem;
  height: 1.25rem;
  margin-top: 0.125rem;
  flex-shrink: 0;
}

.announcement-icon.blue {
  color: #60a5fa;
}
.announcement-icon.green {
  color: #4ade80;
}
.announcement-icon.yellow {
  color: #facc15;
}

.announcement-content {
  flex: 1;
  min-width: 0;
}

.announcement-title {
  color: white;
  font-size: 0.875rem;
  font-weight: 500;
  margin-bottom: 0.25rem;
}

.announcement-text {
  color: #cbd5e1;
  font-size: 0.75rem;
  line-height: 1.5;
  margin-bottom: 0.5rem;
}

.announcement-date {
  color: #64748b;
  font-size: 0.75rem;
}

.rule-item {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  padding: 0.75rem;
  background: rgba(30, 41, 59, 0.3);
  border-radius: 0.5rem;
  margin-bottom: 0.75rem;
}

.rule-number {
  width: 1.5rem;
  height: 1.5rem;
  border-radius: 50%;
  background: rgba(59, 130, 246, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  margin-top: 0.125rem;
}

.rule-number span {
  color: #93c5fd;
  font-size: 0.75rem;
  font-weight: 600;
}

.rule-text {
  color: #cbd5e1;
  font-size: 0.875rem;
  line-height: 1.5;
}

.social-btn {
  width: 100%;
  height: 3rem;
  border: none;
  border-radius: 0.375rem;
  color: white;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
  transition: all 0.2s;
  text-decoration: none;
}

.social-btn:hover {
  transform: scale(1.05);
}

.social-btn.discord {
  background: #5865f2;
}
.social-btn.discord:hover {
  background: #4752c4;
}
.social-btn.website {
  background: #2563eb;
}
.social-btn.website:hover {
  background: #1d4ed8;
}
.social-btn.youtube {
  background: #dc2626;
}
.social-btn.youtube:hover {
  background: #b91c1c;
}
.social-btn.instagram {
  background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
}
.social-btn.instagram:hover {
  background: linear-gradient(45deg, #e6683c 0%, #dc2743 25%, #cc2366 50%, #bc1888 75%, #a91a7a 100%);
}
.social-btn.tiktok {
  background: #000000;
}
.social-btn.tiktok:hover {
  background: #1a1a1a;
}

/* Loading Container */
.loading-container {
  position: absolute;
  bottom: 8rem;
  left: 50%;
  transform: translateX(-50%);
  pointer-events: auto;
}

.loading-card {
  background: rgba(15, 23, 42, 0.95);
  border: 1px solid #475569;
  border-radius: 0.5rem;
  backdrop-filter: blur(12px);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
  padding: 1.5rem;
  width: 24rem;
}

.loading-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.loading-title {
  color: white;
  font-size: 1.125rem;
  font-weight: 600;
}

.loading-badge {
  background: linear-gradient(to right, rgba(59, 130, 246, 0.2), rgba(168, 85, 247, 0.2));
  color: #93c5fd;
  border: 1px solid rgba(59, 130, 246, 0.3);
  padding: 0.25rem 0.75rem;
  border-radius: 0.375rem;
  font-size: 0.875rem;
}

.progress-container {
  position: relative;
  margin-bottom: 1rem;
}

.progress-bar {
  height: 0.75rem;
  background: rgba(51, 65, 85, 0.5);
  border-radius: 9999px;
  overflow: hidden;
  position: relative;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(to right, #3b82f6, #a855f7, #ec4899);
  border-radius: 9999px;
  transition: width 0.5s ease-out;
  width: 0%;
  position: relative;
}

.progress-pulse {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 9999px;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.loading-step {
  color: #cbd5e1;
  font-size: 0.875rem;
  text-align: center;
  font-weight: 500;
}

/* Music Player */
.music-player {
  position: absolute;
  bottom: 1.5rem;
  left: 50%;
  transform: translateX(-50%);
  pointer-events: auto;
  z-index: 100;
}

.music-card {
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.98), rgba(30, 41, 59, 0.95));
  border: 1px solid rgba(99, 102, 241, 0.3);
  border-radius: 1rem;
  backdrop-filter: blur(20px);
  box-shadow:
    0 25px 50px rgba(0, 0, 0, 0.4),
    0 0 0 1px rgba(255, 255, 255, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  padding: 1.25rem;
  display: flex;
  align-items: center;
  gap: 1.25rem;
  min-width: 400px;
  transition: all 0.3s ease;
}

.music-card:hover {
  transform: translateY(-2px);
  box-shadow:
    0 35px 60px rgba(0, 0, 0, 0.5),
    0 0 0 1px rgba(99, 102, 241, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

.album-art {
  width: 4rem;
  height: 4rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 0.75rem;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  position: relative;
  overflow: hidden;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
}

.album-art:hover {
  transform: scale(1.05);
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.4);
}

.album-art img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 0.75rem;
}

.album-art i {
  color: white;
  font-size: 1.75rem;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.track-info {
  flex: 1;
  min-width: 0;
}

.track-name {
  color: white;
  font-size: 1rem;
  font-weight: 600;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 0.25rem;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.track-subtitle {
  color: #a5b4fc;
  font-size: 0.8rem;
  margin-bottom: 0.5rem;
  font-weight: 400;
}

.track-progress {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.time-current,
.time-total {
  color: #94a3b8;
  font-size: 0.75rem;
}

.progress-track {
  flex: 1;
  height: 0.375rem;
  background: rgba(71, 85, 105, 0.6);
  border-radius: 9999px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.2s ease;
}

.progress-track:hover {
  height: 0.5rem;
  background: rgba(71, 85, 105, 0.8);
}

.progress-track-fill {
  width: 0%;
  height: 100%;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  border-radius: 9999px;
  transition: width 0.3s ease;
  position: relative;
}

.progress-track-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.3) 50%, transparent 100%);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.music-controls {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.control-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  width: 2.25rem;
  height: 2.25rem;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.control-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.control-btn:active {
  transform: translateY(0);
}

.play-btn {
  width: 3rem;
  height: 3rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: 2px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.play-btn:hover {
  background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
  box-shadow: 0 12px 35px rgba(102, 126, 234, 0.6);
  transform: translateY(-2px) scale(1.05);
}

/* Playlist */
.playlist {
  position: absolute;
  bottom: 100%;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.98), rgba(30, 41, 59, 0.95));
  border: 1px solid rgba(99, 102, 241, 0.3);
  border-radius: 1rem;
  backdrop-filter: blur(20px);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.4);
  margin-bottom: 1rem;
  max-height: 300px;
  overflow: hidden;
  transform: translateY(20px) scale(0.95);
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.playlist.open {
  transform: translateY(0) scale(1);
  opacity: 1;
  visibility: visible;
}

.playlist-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.playlist-header h4 {
  color: white;
  font-size: 1rem;
  font-weight: 600;
  margin: 0;
}

.playlist-close {
  background: transparent;
  border: none;
  color: #94a3b8;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.playlist-close:hover {
  color: white;
  background: rgba(255, 255, 255, 0.1);
}

.playlist-content {
  max-height: 240px;
  overflow-y: auto;
  padding: 0.5rem;
}

.playlist-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-bottom: 0.25rem;
}

.playlist-item:hover {
  background: rgba(255, 255, 255, 0.1);
}

.playlist-item.active {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.3), rgba(118, 75, 162, 0.3));
  border: 1px solid rgba(102, 126, 234, 0.5);
}

.playlist-item-thumbnail {
  width: 3rem;
  height: 3rem;
  border-radius: 0.375rem;
  overflow: hidden;
  flex-shrink: 0;
}

.playlist-item-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.playlist-item-info {
  flex: 1;
  min-width: 0;
}

.playlist-item-name {
  color: white;
  font-size: 0.875rem;
  font-weight: 500;
  margin-bottom: 0.25rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.playlist-item-artist {
  color: #94a3b8;
  font-size: 0.75rem;
}

.playlist-item-duration {
  color: #64748b;
  font-size: 0.75rem;
  flex-shrink: 0;
}

/* Footer */
.footer {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  padding-bottom: 0.5rem;
}

.footer p {
  color: #94a3b8;
  font-size: 0.75rem;
  text-shadow: 0 10px 25px rgba(0, 0, 0, 0.5);
}

/* Responsive */
@media (max-width: 768px) {
  .logo-text {
    font-size: 4rem;
  }

  .menu-buttons {
    width: 16rem;
    right: 1rem;
  }

  .side-panel {
    width: 20rem;
  }

  .side-panel.open {
    transform: translateX(-20rem);
  }

  .loading-card {
    width: 20rem;
  }

  .music-card {
    min-width: 320px;
    padding: 1rem;
    gap: 1rem;
  }

  .album-art {
    width: 3.5rem;
    height: 3.5rem;
  }

  .track-name {
    font-size: 0.875rem;
  }

  .track-subtitle {
    font-size: 0.75rem;
  }

  .control-btn {
    width: 2rem;
    height: 2rem;
  }

  .play-btn {
    width: 2.5rem;
    height: 2.5rem;
  }

  .playlist {
    max-height: 250px;
  }

  .playlist-item-thumbnail {
    width: 2.5rem;
    height: 2.5rem;
  }
}
