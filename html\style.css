* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, sans-serif;
  overflow: hidden;
  height: 100vh;
}

/* Video Background */
.video-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
}

.video-background iframe {
  width: 150%;
  height: 150%;
  transform: scale(1.5);
  object-fit: cover;
}

.video-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
}

/* Main Content */
.main-content {
  position: relative;
  z-index: 10;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Center Logo */
.center-logo {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  pointer-events: none;
}

.logo-text {
  font-size: 6rem;
  font-weight: bold;
  color: white;
  margin-bottom: 1rem;
  letter-spacing: -0.025em;
  text-shadow: 0 25px 50px rgba(0, 0, 0, 0.5);
}

.logo-accent {
  color: #60a5fa;
}

.logo-subtitle {
  color: #e2e8f0;
  font-size: 1.25rem;
  text-shadow: 0 10px 25px rgba(0, 0, 0, 0.5);
}

/* Menu Buttons */
.menu-buttons {
  position: absolute;
  top: 1.5rem;
  right: 1.5rem;
  width: 20rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  pointer-events: auto;
  z-index: 40;
}

.menu-card {
  background: rgba(15, 23, 42, 0.9);
  border: 1px solid #475569;
  border-radius: 0.5rem;
  backdrop-filter: blur(12px);
}

.menu-btn {
  width: 100%;
  padding: 1rem;
  background: transparent;
  border: none;
  color: white;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: background-color 0.2s;
}

.menu-btn:hover {
  background: rgba(30, 41, 59, 0.5);
}

.menu-btn-content {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.menu-chevron {
  width: 1.25rem;
  height: 1.25rem;
  transition: transform 0.3s;
}

.menu-chevron.rotated {
  transform: rotate(180deg);
}

/* Side Panel */
.side-panel {
  position: fixed;
  top: 1.5rem;
  right: -24rem;
  width: 24rem;
  background: rgba(15, 23, 42, 0.95);
  border: 1px solid #475569;
  border-radius: 0.5rem;
  backdrop-filter: blur(12px);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
  transition: right 0.3s ease-in-out;
  z-index: 50;
  max-height: 24rem;
  overflow: hidden;
}

.side-panel.open {
  right: 0rem;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid #475569;
}

.panel-title {
  color: white;
  font-size: 1.125rem;
  font-weight: 600;
}

.panel-close-btn {
  background: transparent;
  border: none;
  color: white;
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
}

.panel-close-btn:hover {
  background: rgba(30, 41, 59, 0.5);
}

.panel-content {
  padding: 1rem;
  max-height: 24rem;
  overflow-y: auto;
}

/* Panel Content Styles */
.announcement-item {
  background: rgba(30, 41, 59, 0.5);
  border-radius: 0.5rem;
  padding: 1rem;
  border: 1px solid #475569;
  margin-bottom: 0.75rem;
}

.announcement-header {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
}

.announcement-icon {
  width: 1.25rem;
  height: 1.25rem;
  margin-top: 0.125rem;
  flex-shrink: 0;
}

.announcement-icon.blue {
  color: #60a5fa;
}
.announcement-icon.green {
  color: #4ade80;
}
.announcement-icon.yellow {
  color: #facc15;
}

.announcement-content {
  flex: 1;
  min-width: 0;
}

.announcement-title {
  color: white;
  font-size: 0.875rem;
  font-weight: 500;
  margin-bottom: 0.25rem;
}

.announcement-text {
  color: #cbd5e1;
  font-size: 0.75rem;
  line-height: 1.5;
  margin-bottom: 0.5rem;
}

.announcement-date {
  color: #64748b;
  font-size: 0.75rem;
}

.rule-item {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  padding: 0.75rem;
  background: rgba(30, 41, 59, 0.3);
  border-radius: 0.5rem;
  margin-bottom: 0.75rem;
}

.rule-number {
  width: 1.5rem;
  height: 1.5rem;
  border-radius: 50%;
  background: rgba(59, 130, 246, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  margin-top: 0.125rem;
}

.rule-number span {
  color: #93c5fd;
  font-size: 0.75rem;
  font-weight: 600;
}

.rule-text {
  color: #cbd5e1;
  font-size: 0.875rem;
  line-height: 1.5;
}

.social-btn {
  width: 100%;
  height: 3rem;
  border: none;
  border-radius: 0.375rem;
  color: white;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
  transition: all 0.2s;
  text-decoration: none;
}

.social-btn:hover {
  transform: scale(1.05);
}

.social-btn.discord {
  background: #5865f2;
}
.social-btn.discord:hover {
  background: #4752c4;
}
.social-btn.website {
  background: #2563eb;
}
.social-btn.website:hover {
  background: #1d4ed8;
}
.social-btn.youtube {
  background: #dc2626;
}
.social-btn.youtube:hover {
  background: #b91c1c;
}

/* Loading Container */
.loading-container {
  position: absolute;
  bottom: 8rem;
  left: 50%;
  transform: translateX(-50%);
  pointer-events: auto;
}

.loading-card {
  background: rgba(15, 23, 42, 0.95);
  border: 1px solid #475569;
  border-radius: 0.5rem;
  backdrop-filter: blur(12px);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
  padding: 1.5rem;
  width: 24rem;
}

.loading-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.loading-title {
  color: white;
  font-size: 1.125rem;
  font-weight: 600;
}

.loading-badge {
  background: linear-gradient(to right, rgba(59, 130, 246, 0.2), rgba(168, 85, 247, 0.2));
  color: #93c5fd;
  border: 1px solid rgba(59, 130, 246, 0.3);
  padding: 0.25rem 0.75rem;
  border-radius: 0.375rem;
  font-size: 0.875rem;
}

.progress-container {
  position: relative;
  margin-bottom: 1rem;
}

.progress-bar {
  height: 0.75rem;
  background: rgba(51, 65, 85, 0.5);
  border-radius: 9999px;
  overflow: hidden;
  position: relative;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(to right, #3b82f6, #a855f7, #ec4899);
  border-radius: 9999px;
  transition: width 0.5s ease-out;
  width: 0%;
  position: relative;
}

.progress-pulse {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 9999px;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.loading-step {
  color: #cbd5e1;
  font-size: 0.875rem;
  text-align: center;
  font-weight: 500;
}

/* Music Player */
.music-player {
  position: absolute;
  bottom: 1.5rem;
  left: 50%;
  transform: translateX(-50%);
  pointer-events: auto;
}

.music-card {
  background: linear-gradient(to right, rgba(15, 23, 42, 0.95), rgba(30, 41, 59, 0.95));
  border: 1px solid #475569;
  border-radius: 0.5rem;
  backdrop-filter: blur(12px);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
  padding: 1rem;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.album-art {
  width: 3rem;
  height: 3rem;
  background: linear-gradient(to bottom right, #3b82f6, #a855f7);
  border-radius: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.album-art i {
  color: white;
  font-size: 1.5rem;
}

.track-info {
  flex: 1;
  min-width: 0;
}

.track-name {
  color: white;
  font-size: 0.875rem;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 0.25rem;
}

.track-subtitle {
  color: #94a3b8;
  font-size: 0.75rem;
  margin-bottom: 0.25rem;
}

.track-progress {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.time-current,
.time-total {
  color: #94a3b8;
  font-size: 0.75rem;
}

.progress-track {
  flex: 1;
  height: 0.25rem;
  background: #475569;
  border-radius: 9999px;
  overflow: hidden;
}

.progress-track-fill {
  width: 33%;
  height: 100%;
  background: linear-gradient(to right, #3b82f6, #a855f7);
  border-radius: 9999px;
}

.music-controls {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.control-btn {
  background: transparent;
  border: none;
  color: white;
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
}

.control-btn:hover {
  background: rgba(51, 65, 85, 0.5);
}

.play-btn {
  width: 2.5rem;
  height: 2.5rem;
  background: linear-gradient(to right, rgba(59, 130, 246, 0.2), rgba(168, 85, 247, 0.2));
}

/* Footer */
.footer {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  padding-bottom: 0.5rem;
}

.footer p {
  color: #94a3b8;
  font-size: 0.75rem;
  text-shadow: 0 10px 25px rgba(0, 0, 0, 0.5);
}

/* Responsive */
@media (max-width: 768px) {
  .logo-text {
    font-size: 4rem;
  }

  .menu-buttons {
    width: 16rem;
    right: 1rem;
  }

  .side-panel {
    width: 20rem;
    right: -20rem;
  }

  .side-panel.open {
    right: 0rem;
  }

  .loading-card {
    width: 20rem;
  }
}
