# Lotus Loading Screen

Modern ve özelleştirilebilir FiveM loading screen scripti by Lotus Development.

## Ö<PERSON><PERSON>ler

- ✅ **Tamamen Özelleştirilebilir**: Config.js ile tüm ayarlar
- ✅ **YouTube Arkaplan Videosu**: Otomatik oynatma desteği
- ✅ **Müzik Çalar**: Çoklu şarkı desteği
- ✅ **Duyurular Sistemi**: Renkli ve kategorize edilmiş duyurular
- ✅ **<PERSON><PERSON><PERSON>**: Numaralandırılmış kural listesi
- ✅ **Sosyal Medya Linkleri**: Discord, Website, YouTube, Instagram, TikTok
- ✅ **Responsive Tasarım**: Mobil uyumlu
- ✅ **Animasyonlu Panel**: Sağdan sola kayarak açılan menü
- ✅ **Gerçek Loading Progress**: Client.lua ile gerçek yükleme takibi
- ✅ **FiveM Entegrasyonu**: Otomatik loading screen kapatma
- ✅ **Export Fonksiyonlar<PERSON>**: <PERSON>ğer scriptlerle entegrasyon
- ✅ **<PERSON><PERSON>**: Loading kontrolü için komutlar

## Kurulum

1. Bu klasörü sunucunuzun `resources` dizinine kopyalayın
2. `server.cfg` dosyanıza `ensure lotus_loading` ekleyin
3. `config.js` dosyasını ihtiyaçlarınıza göre düzenleyin

## Konfigürasyon

### Logo ve Başlık
```javascript
logo: {
    title: "LOTUS",                    // Ana başlık
    titleAccent: "LOADING",            // Vurgulu kısım
    subtitle: "Lotus Development",     // Alt başlık
    imageName: "logo.png",             // images/ klasöründeki logo dosyası
    showImage: true,                   // Logo resmini göster/gizle
    imageSize: "120px"                 // Logo boyutu
}
```

### Arkaplan Videosu
```javascript
backgroundVideo: {
    enabled: true,
    youtubeId: "jfKfPfyJRdk",  // YouTube video ID'si
    autoplay: true,
    muted: true,
    loop: true
}
```

### Duyurular
```javascript
announcements: [
    {
        type: "update",                    // update, event, info
        title: "Yeni Güncelleme v2.1.0",
        content: "Yeni araçlar ve iş sistemi eklendi!",
        date: "15 Aralık 2024",
        icon: "fas fa-exclamation-circle", // FontAwesome icon
        color: "blue"                      // blue, green, yellow
    }
]
```

### Müzik Çalar
```javascript
musicPlayer: {
    enabled: true,
    tracks: [
        {
            name: "Lo-Fi Hip Hop Mix",
            artist: "Arka Plan Müziği",
            duration: "4:12",
            url: "music/lofi-mix.mp3"  // Müzik dosyası yolu
        }
    ],
    autoplay: false,
    volume: 0.5
}
```

### Sosyal Medya Linkleri
```javascript
socialLinks: [
    {
        name: "Discord",
        icon: "fab fa-discord",
        url: "https://discord.gg/cityrp",
        class: "discord"  // CSS class (discord, website, youtube, instagram, tiktok)
    }
]
```

## Logo Dosyası

Logo dosyanızı `images/` klasörüne koyun ve config.js'de dosya adını belirtin.

**Desteklenen formatlar:** PNG, JPG, JPEG, GIF, SVG

**Önerilen boyut:** 200x200px veya 512x512px (kare format)

**Kurulum:**
1. Logo dosyanızı `images/` klasörüne kopyalayın
2. `config.js`'de `imageName` değerini güncelleyin
3. `showImage: true` olarak ayarlayın

**Örnek:**
```
images/logo.png → config.js'de imageName: "logo.png"
images/my-logo.jpg → config.js'de imageName: "my-logo.jpg"
```

**Not:** Logo yüklenemezse otomatik olarak text fallback (ilk harf) gösterilir.

## Müzik Sistemi

Müzik çalar HTML5 Audio kullanarak yerel müzik dosyalarını çalar.

**Müzik Klasör Yapısı:**
```
music/
├── song1.mp3
├── song2.mp3
└── thumbnails/
    ├── song1.jpg
    └── song2.jpg
```

**Desteklenen Formatlar:** MP3, WAV, OGG

**Config Örneği:**
```javascript
tracks: [
    {
        name: "Şarkı Adı",
        artist: "Sanatçı",
        duration: "3:45",
        url: "music/song.mp3",
        thumbnail: "music/thumbnails/song.jpg"
    }
]
```

## Özelleştirme

### Renkler
`style.css` dosyasında CSS değişkenlerini düzenleyerek renkleri değiştirebilirsiniz.

### Animasyonlar
Panel animasyon hızını `transition` değerlerini değiştirerek ayarlayabilirsiniz.

### Responsive
Mobil cihazlar için `@media` sorguları ile özelleştirmeler yapabilirsiniz.

## FiveM Entegrasyonu

### Export Fonksiyonları
Diğer scriptlerden kullanabileceğiniz fonksiyonlar:

```lua
-- Loading progress güncelleme
exports['lotus_loading']:updateProgress(75, "Özel işlem yapılıyor...")

-- Loading screen'i zorla kapatma
exports['lotus_loading']:finishLoading()

-- Loading durumunu kontrol etme
local isFinished = exports['lotus_loading']:isLoadingFinished()
```

### Admin Komutları
```lua
-- Konsol komutu
forceloading <player_id>

-- Oyun içi komut (admin yetkisi gerekli)
/forceloading <player_id>
```

### Events
```lua
-- Client-side events
AddEventHandler('lotus_loading:finished', function()
    -- Loading tamamlandığında çalışır
end)

AddEventHandler('lotus_loading:playerReady', function(playerId)
    -- Bir oyuncu loading'i tamamladığında çalışır
end)

-- Server-side events
AddEventHandler('lotus_loading:playerReady', function(playerId)
    -- Sunucu tarafında oyuncu hazır olduğunda çalışır
end)
```

## Debug Modu

Development için debug modu:
```
set lotus_loading_debug true
```

Debug komutları:
- `loading_finish` - Loading'i zorla bitir
- `loading_progress <0-100>` - Progress'i manuel ayarla

## Destek

Herhangi bir sorun yaşarsanız:
1. Config.js dosyasının doğru yüklendiğinden emin olun
2. Konsol hatalarını kontrol edin
3. Dosya yollarının doğru olduğunu kontrol edin
4. FiveM client/server loglarını kontrol edin

## Lisans

Bu proje MIT lisansı altında lisanslanmıştır.

## Versiyon

v2.1.0 - Aralık 2024
