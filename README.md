# CityRP Loading Screen

Modern ve özelleştirilebilir FiveM loading screen scripti.

## Özellikler

- ✅ **Tamamen Özelleştirilebilir**: Config.js ile tüm ayarlar
- ✅ **YouTube Arkaplan Videosu**: Otomatik oynatma desteği
- ✅ **Müzik Çalar**: Çoklu şarkı desteği
- ✅ **Duyurular Sistemi**: Renkli ve kategorize edilmiş duyurular
- ✅ **<PERSON><PERSON><PERSON>**: Numaralandırılmış kural listesi
- ✅ **Sosyal Medya Linkleri**: Discord, Website, YouTube, Instagram, TikTok
- ✅ **Responsive Tasarım**: Mobil uyumlu
- ✅ **Animasyonlu Panel**: Sağdan sola kayarak açılan menü
- ✅ **Loading Progress**: Gerçek zamanlı yükleme göstergesi

## Kurulum

1. Bu klasörü sunucunuzun `resources` dizinine kopyalayın
2. `server.cfg` dosyanıza `ensure lotus_loading` ekleyin
3. `config.js` dosyasını ihtiyaçlarınıza göre düzenleyin

## Konfigürasyon

### Logo ve Başlık
```javascript
logo: {
    title: "CITY",           // Ana başlık
    titleAccent: "RP",       // Vurgulu kısım
    subtitle: "Gerçekçi Roleplay Deneyimi"  // Alt başlık
}
```

### Arkaplan Videosu
```javascript
backgroundVideo: {
    enabled: true,
    youtubeId: "jfKfPfyJRdk",  // YouTube video ID'si
    autoplay: true,
    muted: true,
    loop: true
}
```

### Duyurular
```javascript
announcements: [
    {
        type: "update",                    // update, event, info
        title: "Yeni Güncelleme v2.1.0",
        content: "Yeni araçlar ve iş sistemi eklendi!",
        date: "15 Aralık 2024",
        icon: "fas fa-exclamation-circle", // FontAwesome icon
        color: "blue"                      // blue, green, yellow
    }
]
```

### Müzik Çalar
```javascript
musicPlayer: {
    enabled: true,
    tracks: [
        {
            name: "Lo-Fi Hip Hop Mix",
            artist: "Arka Plan Müziği",
            duration: "4:12",
            url: "music/lofi-mix.mp3"  // Müzik dosyası yolu
        }
    ],
    autoplay: false,
    volume: 0.5
}
```

### Sosyal Medya Linkleri
```javascript
socialLinks: [
    {
        name: "Discord",
        icon: "fab fa-discord",
        url: "https://discord.gg/cityrp",
        class: "discord"  // CSS class (discord, website, youtube, instagram, tiktok)
    }
]
```

## Müzik Dosyaları

Müzik dosyalarınızı `html/music/` klasörüne koyun ve config.js'de URL'leri güncelleyin.

Desteklenen formatlar: MP3, WAV, OGG

## Özelleştirme

### Renkler
`style.css` dosyasında CSS değişkenlerini düzenleyerek renkleri değiştirebilirsiniz.

### Animasyonlar
Panel animasyon hızını `transition` değerlerini değiştirerek ayarlayabilirsiniz.

### Responsive
Mobil cihazlar için `@media` sorguları ile özelleştirmeler yapabilirsiniz.

## Destek

Herhangi bir sorun yaşarsanız:
1. Config.js dosyasının doğru yüklendiğinden emin olun
2. Konsol hatalarını kontrol edin
3. Dosya yollarının doğru olduğunu kontrol edin

## Lisans

Bu proje MIT lisansı altında lisanslanmıştır.

## Versiyon

v2.1.0 - Aralık 2024
