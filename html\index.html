<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CityRP Loading Screen</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="../config.js"></script>
    <script src="https://www.youtube.com/iframe_api"></script>
</head>
<body>
    <!-- YouTube Background Video -->
    <div class="video-background" id="videoBackground">
        <iframe
            id="backgroundVideo"
            title="Background Video"
            frameborder="0"
            allow="autoplay; encrypted-media"
            allowfullscreen>
        </iframe>
        <div class="video-overlay"></div>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Center Logo -->
        <div class="center-logo">
            <div class="logo-container">
                <div class="logo-circle" id="logoCircle">
                    <div class="logo-image-container" id="logoImageContainer">
                        <img id="logoImage" src="" alt="Logo" style="display: none;">
                        <div class="logo-text-fallback" id="logoTextFallback">
                            <span id="logoInitials">L</span>
                        </div>
                    </div>
                    <svg class="loading-circle" viewBox="0 0 100 100">
                        <circle cx="50" cy="50" r="45" class="loading-circle-bg"></circle>
                        <circle cx="50" cy="50" r="45" class="loading-circle-progress" id="loadingCircleProgress"></circle>
                    </svg>
                </div>
                <h1 class="logo-text" id="logoTitle"><span class="logo-accent" id="logoAccent"></span></h1>
                <p class="logo-subtitle" id="logoSubtitle"></p>
            </div>
        </div>

        <!-- Right Side Menu Buttons -->
        <div class="menu-buttons">
            <!-- Announcements Button -->
            <div class="menu-card">
                <button class="menu-btn" onclick="toggleSection('announcements')">
                    <span class="menu-btn-content">
                        <i class="fas fa-bell"></i>
                        Duyurular
                    </span>
                    <i class="fas fa-chevron-right menu-chevron" id="announcements-chevron"></i>
                </button>
            </div>

            <!-- Server Rules Button -->
            <div class="menu-card">
                <button class="menu-btn" onclick="toggleSection('rules')">
                    <span class="menu-btn-content">Sunucu Kuralları</span>
                    <i class="fas fa-chevron-right menu-chevron" id="rules-chevron"></i>
                </button>
            </div>

            <!-- Community Button -->
            <div class="menu-card">
                <button class="menu-btn" onclick="toggleSection('social')">
                    <span class="menu-btn-content">Topluluk</span>
                    <i class="fas fa-chevron-right menu-chevron" id="social-chevron"></i>
                </button>
            </div>
        </div>

        <!-- Side Panel -->
        <div class="side-panel" id="sidePanel">
            <div class="panel-header">
                <h3 class="panel-title" id="panelTitle">Panel</h3>
                <button class="panel-close-btn" onclick="closePanel()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="panel-content" id="panelContent">
                <!-- Content will be dynamically loaded here -->
            </div>
        </div>

        <!-- Loading Progress -->
        <div class="loading-container">
            <div class="loading-card">
                <div class="loading-header">
                    <h3 class="loading-title">Yükleniyor</h3>
                    <div class="loading-badge">
                        <span id="progressText">0%</span>
                    </div>
                </div>
                <div class="progress-container">
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill"></div>
                        <div class="progress-pulse"></div>
                    </div>
                </div>
                <p class="loading-step" id="loadingStep">Bağlanıyor...</p>
            </div>
        </div>

        <!-- Music Player -->
        <div class="music-player" id="musicPlayer">
            <div class="music-card">
                <div class="album-art" id="albumArt">
                    <img id="trackThumbnail" src="" alt="Track Thumbnail" style="display: none;">
                    <i class="fas fa-music" id="defaultIcon"></i>
                </div>
                <div class="track-info">
                    <p class="track-name" id="trackName">Müzik Seçin</p>
                    <p class="track-subtitle" id="trackArtist">Sanatçı</p>
                    <div class="track-progress">
                        <span class="time-current" id="currentTime">0:00</span>
                        <div class="progress-track" id="progressTrack">
                            <div class="progress-track-fill" id="progressTrackFill"></div>
                        </div>
                        <span class="time-total" id="totalTime">0:00</span>
                    </div>
                </div>
                <div class="music-controls">
                    <button class="control-btn" onclick="previousTrack()" title="Önceki Şarkı">
                        <i class="fas fa-step-backward"></i>
                    </button>
                    <button class="control-btn play-btn" onclick="togglePlay()" id="playBtn" title="Oynat/Duraklat">
                        <i class="fas fa-play" id="playIcon"></i>
                    </button>
                    <button class="control-btn" onclick="nextTrack()" title="Sonraki Şarkı">
                        <i class="fas fa-step-forward"></i>
                    </button>
                    <button class="control-btn" onclick="toggleMute()" id="muteBtn" title="Ses Aç/Kapat">
                        <i class="fas fa-volume-up" id="muteIcon"></i>
                    </button>
                    <button class="control-btn" onclick="togglePlaylist()" id="playlistBtn" title="Şarkı Listesi">
                        <i class="fas fa-list"></i>
                    </button>
                </div>
            </div>

            <!-- Playlist -->
            <div class="playlist" id="playlist">
                <div class="playlist-header">
                    <h4>Şarkı Listesi</h4>
                    <button class="playlist-close" onclick="togglePlaylist()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="playlist-content" id="playlistContent">
                    <!-- Playlist items will be generated here -->
                </div>
            </div>
        </div>

        <!-- Hidden YouTube Player -->
        <div id="youtubePlayerContainer" style="display: none;">
            <div id="youtubePlayer"></div>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p id="footerText"></p>
        </div>
    </div>

    <!-- SVG Definitions -->
    <svg style="display: none;">
        <defs>
            <linearGradient id="circleGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" style="stop-color:#ff6b35;stop-opacity:1" />
                <stop offset="100%" style="stop-color:#ffa726;stop-opacity:1" />
            </linearGradient>
        </defs>
    </svg>

    <script src="script.js"></script>
</body>
</html>
